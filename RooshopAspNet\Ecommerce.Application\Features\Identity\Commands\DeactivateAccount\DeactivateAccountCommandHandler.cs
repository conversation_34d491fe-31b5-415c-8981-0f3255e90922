using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.DeactivateAccount
{
    /// <summary>
    /// 用户注销账户命令处理器
    /// </summary>
    public class DeactivateAccountCommandHandler : IRequestHandler<DeactivateAccountCommand, DeactivateAccountResponse>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICacheService _cacheService;
        private readonly ILogger<DeactivateAccountCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeactivateAccountCommandHandler(
            UserManager<ApplicationUser> userManager,
            ICacheService cacheService,
            ILogger<DeactivateAccountCommandHandler> logger)
        {
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 处理用户注销账户命令
        /// </summary>
        public async Task<DeactivateAccountResponse> Handle(DeactivateAccountCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理用户注销账户命令: {UserId}", request.UserId);

                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId);
                if (user == null)
                {
                    _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_NOT_FOUND",
                        Errors = new[] { "用户不存在" }
                    };
                }

                // 检查用户当前状态
                if (user.Status == UserStatus.Deleted)
                {
                    _logger.LogWarning("用户已被删除，无法注销: {UserId}", request.UserId);
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_DELETED",
                        Errors = new[] { "账户已被删除" }
                    };
                }

                if (user.Status == UserStatus.Inactive)
                {
                    _logger.LogWarning("用户已被注销: {UserId}", request.UserId);
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_ALREADY_DEACTIVATED",
                        Errors = new[] { "账户已被注销" }
                    };
                }

                // 验证密码
                var passwordValid = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordValid)
                {
                    _logger.LogWarning("用户注销账户时密码验证失败: {UserId}", request.UserId);
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "INVALID_PASSWORD",
                        Errors = new[] { "密码验证失败" }
                    };
                }

                // 检查是否为系统用户（系统用户不能自己注销）
                var roles = await _userManager.GetRolesAsync(user);
                var isSystemUser = roles.Any(role => role != "Customer");
                if (isSystemUser)
                {
                    _logger.LogWarning("系统用户尝试注销账户: {UserId}", request.UserId);
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "SYSTEM_USER_CANNOT_DEACTIVATE",
                        Errors = new[] { "系统用户不能自己注销账户，请联系管理员" }
                    };
                }

                DateTime? effectiveDate;
                DateTime? gracePeriodEnd = null;
                bool hasGracePeriod = false;

                if (request.ImmediateDeactivation)
                {
                    // 立即注销
                    user.DeactivateImmediately(request.Reason);
                    effectiveDate = DateTime.UtcNow;
                    _logger.LogInformation("用户立即注销账户: {UserId}, 原因: {Reason}", 
                        request.UserId, request.Reason ?? "未提供");
                }
                else
                {
                    // 安排延期注销（7天反悔期）
                    user.ScheduleDeactivation(request.Reason, 7);
                    effectiveDate = user.DeactivationScheduledAt;
                    gracePeriodEnd = effectiveDate;
                    hasGracePeriod = true;
                    _logger.LogInformation("用户安排延期注销账户: {UserId}, 生效时间: {EffectiveDate}, 原因: {Reason}", 
                        request.UserId, effectiveDate, request.Reason ?? "未提供");
                }

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                // 更新用户
                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = result.Errors.Select(e => e.Description).ToArray();
                    _logger.LogError("注销用户账户失败: {UserId}, 错误: {Errors}", request.UserId, string.Join(", ", errors));
                    return new DeactivateAccountResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "DEACTIVATION_FAILED",
                        Errors = errors
                    };
                }

                // 清除缓存
                try
                {
                    await _cacheService.RemoveByPatternAsync($"*:Users:{request.UserId}*");
                    await _cacheService.RemoveByPatternAsync($"*:Users:List*");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清除用户缓存时发生错误: {UserId}", request.UserId);
                }

                return new DeactivateAccountResponse
                {
                    IsSuccessful = true,
                    UserId = user.Id.ToString(),
                    UserName = user.UserName,
                    Status = user.Status.ToString(),
                    EffectiveDate = effectiveDate,
                    HasGracePeriod = hasGracePeriod,
                    GracePeriodEnd = gracePeriodEnd
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理用户注销账户时发生错误: {UserId}, 错误: {Message}", request.UserId, ex.Message);
                return new DeactivateAccountResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "UNEXPECTED_ERROR",
                    Errors = new[] { ex.Message }
                };
            }
        }
    }
}
