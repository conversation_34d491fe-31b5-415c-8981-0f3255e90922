using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace Ecommerce.Application.Features.Identity.Commands.Login;

public class LoginCommandHandler : IRequestHandler<LoginCommand, LoginResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenService _tokenService;
    private readonly ILogger<LoginCommandHandler> _logger;

    public LoginCommandHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenService tokenService,
        ILogger<LoginCommandHandler> logger)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenService = tokenService;
        _logger = logger;
    }

    public async Task<LoginResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        // 尝试通过用户名或电子邮箱查找用户
        var user = await _userManager.FindByNameAsync(request.UsernameOrEmail);
        if (user == null)
        {
            // 如果用户名未找到，尝试通过电子邮箱查找
            user = await _userManager.FindByEmailAsync(request.UsernameOrEmail);

            if (user == null)
            {
                return new LoginResponse
                {
                    IsSuccessful = false,
                    Errors = new[] { "用户名或密码错误" }
                };
            }
        }

        // 记录登录前的安全戳
        string originalSecurityStamp = user.SecurityStamp ?? string.Empty;
        _logger.LogInformation("登录前的安全戳: {SecurityStamp}", originalSecurityStamp);

        // 检查邮箱是否已确认
        if (_userManager.Options.SignIn.RequireConfirmedEmail && !await _userManager.IsEmailConfirmedAsync(user))
        {
            return new LoginResponse
            {
                IsSuccessful = false,
                Errors = new[] { "邮箱未验证，请先验证邮箱" },
                RequiresEmailConfirmation = true
            };
        }

        var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
        if (!result.Succeeded)
        {
            return new LoginResponse
            {
                IsSuccessful = false,
                Errors = new[] { "用户名或密码错误" }
            };
        }

        // 更新最后登录时间
        user.SetLastLoginTime();
        await _userManager.UpdateAsync(user);

        // 记录登录后的安全戳
        string currentSecurityStamp = user.SecurityStamp ?? string.Empty;
        _logger.LogInformation("登录后的安全戳: {SecurityStamp}", currentSecurityStamp);

        // 检查安全戳是否被更新
        if (originalSecurityStamp != currentSecurityStamp)
        {
            _logger.LogWarning("安全戳在登录过程中被更新！原始值: {OriginalStamp}, 新值: {NewStamp}",
                originalSecurityStamp, currentSecurityStamp);
        }
        else
        {
            _logger.LogInformation("安全戳在登录过程中未变化");
        }

        var roles = await _userManager.GetRolesAsync(user);
        var token = await _tokenService.CreateAccessTokenAsync(user.Id, user.UserName!, user.Email!, roles);
        var refreshToken = await _tokenService.CreateRefreshTokenAsync(user);
        var expiresIn = _tokenService.GetAccessTokenExpirationSeconds();

        // 记录生成令牌后的安全戳
        string finalSecurityStamp = user.SecurityStamp ?? string.Empty;
        _logger.LogInformation("生成令牌后的安全戳: {SecurityStamp}", finalSecurityStamp);

        // 再次检查安全戳是否被更新
        if (currentSecurityStamp != finalSecurityStamp)
        {
            _logger.LogWarning("安全戳在生成令牌过程中被更新！登录后值: {LoginStamp}, 生成令牌后值: {FinalStamp}",
                currentSecurityStamp, finalSecurityStamp);
        }
        else
        {
            _logger.LogInformation("安全戳在生成令牌过程中未变化");
        }

        return new LoginResponse
        {
            UserId = user.Id.ToString(),
            UserName = user.UserName!,
            Email = user.Email!,
            Token = token,
            RefreshToken = refreshToken,
            ExpiresIn = expiresIn,
            Roles = roles.ToArray(),
            IsSuccessful = true
        };
    }
}