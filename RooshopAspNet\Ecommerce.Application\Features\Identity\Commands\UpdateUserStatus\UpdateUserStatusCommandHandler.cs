using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.UpdateUserStatus;

/// <summary>
/// 更新用户状态命令处理器
/// </summary>
public class UpdateUserStatusCommandHandler : IRequestHandler<UpdateUserStatusCommand, UpdateUserStatusResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICacheService _cacheService;
    private readonly ILogger<UpdateUserStatusCommandHandler> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public UpdateUserStatusCommandHandler(
        UserManager<ApplicationUser> userManager,
        ICacheService cacheService,
        ILogger<UpdateUserStatusCommandHandler> logger)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理更新用户状态命令
    /// </summary>
    public async Task<UpdateUserStatusResponse> Handle(UpdateUserStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始处理更新用户状态命令: {UserId}, 状态: {Status}", request.UserId, request.Status);

            // 获取用户
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null)
            {
                _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                return new UpdateUserStatusResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "USER_NOT_FOUND",
                    Errors = new[] { "用户不存在" }
                };
            }

            // 检查是否为系统管理员
            var isAdmin = await _userManager.IsInRoleAsync(user, "Administrator");
            if (isAdmin && request.Status.ToLower() != "active")
            {
                var adminCount = (await _userManager.GetUsersInRoleAsync("Administrator")).Count;
                if (adminCount <= 1)
                {
                    _logger.LogWarning("尝试禁用最后一个管理员: {UserId}", request.UserId);
                    return new UpdateUserStatusResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "CANNOT_DISABLE_LAST_ADMIN",
                        Errors = new[] { "不能禁用最后一个管理员账户" }
                    };
                }
            }

            // 更新用户状态
            switch (request.Status.ToLower())
            {
                case "active":
                    user.Activate();
                    user.LockoutEnabled = false;
                    user.LockoutEnd = null;
                    break;
                case "inactive":
                    user.Deactivate();
                    user.LockoutEnabled = true;
                    user.LockoutEnd = null;
                    break;
                case "suspended":
                    user.Suspend();
                    user.LockoutEnabled = true;
                    user.LockoutEnd = DateTimeOffset.UtcNow.AddYears(1); // 暂停1年
                    break;
                case "locked":
                    user.LockoutEnabled = true;
                    user.LockoutEnd = DateTimeOffset.UtcNow.AddYears(100); // 长期锁定
                    break;
                default:
                    _logger.LogWarning("无效的用户状态: {Status}", request.Status);
                    return new UpdateUserStatusResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "INVALID_STATUS",
                        Errors = new[] { "无效的用户状态，支持的状态：active, inactive, suspended, locked" }
                    };
            }

            // 更新用户
            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                var errors = result.Errors.Select(e => e.Description).ToArray();
                _logger.LogError("更新用户状态失败: {UserId}, 状态: {Status}, 错误: {Errors}",
                    request.UserId, request.Status, string.Join(", ", errors));
                return new UpdateUserStatusResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "UPDATE_FAILED",
                    Errors = errors
                };
            }

            // 如果锁定用户，则更新安全戳，使所有现有令牌失效
            if (request.Status.ToLower() == "locked" || request.Status.ToLower() == "inactive")
            {
                await _userManager.UpdateSecurityStampAsync(user);
            }

            // 清除缓存
            try
            {
                // 清除用户相关缓存
                await _cacheService.RemoveByPatternAsync($"*:Users:{request.UserId}*");
                await _cacheService.RemoveByPatternAsync($"*:Users:List*");
            }
            catch (Exception ex)
            {
                // 清除缓存失败不应影响主要操作，只记录日志
                _logger.LogError(ex, "清除用户缓存时发生错误: {UserId}", request.UserId);
            }

            _logger.LogInformation("成功更新用户状态: {UserId}, 状态: {Status}", request.UserId, request.Status);
            return new UpdateUserStatusResponse
            {
                UserId = user.Id.ToString(),
                UserName = user.UserName,
                Status = request.Status,
                IsSuccessful = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户状态时发生错误: {UserId}, 状态: {Status}, 错误: {Message}",
                request.UserId, request.Status, ex.Message);
            return new UpdateUserStatusResponse
            {
                IsSuccessful = false,
                ErrorCode = "UNEXPECTED_ERROR",
                Errors = new[] { ex.Message }
            };
        }
    }
}
