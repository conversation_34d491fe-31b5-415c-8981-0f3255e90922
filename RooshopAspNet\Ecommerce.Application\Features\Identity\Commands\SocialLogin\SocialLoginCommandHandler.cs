using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System.Threading;
using System.Threading.Tasks;

namespace Ecommerce.Application.Features.Identity.Commands.SocialLogin;

public class SocialLoginCommandHandler : IRequestHandler<SocialLoginCommand, SocialLoginResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenService _tokenService;

    public SocialLoginCommandHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenService tokenService)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenService = tokenService;
    }

    public async Task<SocialLoginResponse> Handle(SocialLoginCommand request, CancellationToken cancellationToken)
    {
        var info = await _signInManager.GetExternalLoginInfoAsync();
        if (info == null)
        {
            return new SocialLoginResponse
            {
                IsSuccessful = false,
                Errors = new[] { "无法获取外部登录信息" }
            };
        }

        var result = await _signInManager.ExternalLoginSignInAsync(info.LoginProvider, info.ProviderKey, false);
        if (result.Succeeded)
        {
            var user = await _userManager.FindByLoginAsync(info.LoginProvider, info.ProviderKey);
            if (user != null)
            {
                // 更新最后登录时间
                user.SetLastLoginTime();
                await _userManager.UpdateAsync(user);

                var roles = await _userManager.GetRolesAsync(user);
                var token = await _tokenService.CreateAccessTokenAsync(user.Id, user.UserName!, user.Email!, roles);

                return new SocialLoginResponse
                {
                    Id = user.Id.ToString(),
                    UserName = user.UserName!,
                    Email = user.Email!,
                    Token = token,
                    IsSuccessful = true
                };
            }
        }

        return new SocialLoginResponse
        {
            IsSuccessful = false,
            Errors = new[] { "社交登录失败" }
        };
    }
}