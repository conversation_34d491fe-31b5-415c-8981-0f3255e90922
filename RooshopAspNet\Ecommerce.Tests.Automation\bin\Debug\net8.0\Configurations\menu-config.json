{"Menus": [{"Id": "dashboard", "Component": "BasicLayout", "Name": "Dashboard", "Path": "/", "Redirect": "/dashboard", "Order": -1, "Title": "仪表盘", "RequiredPermissions": ["Permissions.Menus.Dashboard"], "Children": [{"Id": "dashboard-index", "Name": "Dashboard", "Path": "/dashboard", "Component": "/dashboard/index", "Title": "仪表盘", "Icon": "DashboardOutlined", "Affix": true, "RequiredPermissions": ["Permissions.Menus.Dashboard"]}]}, {"Id": "user-permission-management", "Component": "BasicLayout", "Name": "UserPermissionManagement", "Path": "/user", "Redirect": "/user/list", "Order": 1, "Title": "用户与权限管理", "Icon": "UserOutlined", "RequiredPermissions": ["Permissions.Menus.UserManagement"], "Children": [{"Id": "user-list", "Name": "UserList", "Path": "/user/list", "Component": "/user/list/index", "Title": "用户列表", "RequiredPermissions": ["Permissions.Users.View"]}, {"Id": "role-list", "Name": "RoleList", "Path": "/user/role", "Component": "/user/role/index", "Title": "角色管理", "RequiredPermissions": ["Permissions.Roles.View"]}, {"Id": "permission-list", "Name": "PermissionList", "Path": "/user/permission", "Component": "/user/permission/index", "Title": "权限管理", "RequiredPermissions": ["Permissions.Roles.View"]}, {"Id": "permission-groups", "Name": "PermissionGroups", "Path": "/user/permission-groups", "Component": "/user/permission-groups/index", "Title": "权限组管理", "RequiredPermissions": ["Permissions.Roles.AssignPermissions"]}, {"Id": "user-address", "Name": "User<PERSON>ddress", "Path": "/user/address", "Component": "/user/address/index", "Title": "用户地址管理", "RequiredPermissions": ["Permissions.Users.View"]}, {"Id": "user-roles", "Name": "UserRoles", "Path": "/user/user-roles", "Component": "/user/user-roles/index", "Title": "用户角色管理", "RequiredPermissions": ["Permissions.Users.EditUsers"]}, {"Id": "role-inheritance", "Name": "RoleInheritance", "Path": "/user/inheritance", "Component": "/role/inheritance/index", "Title": "角色继承管理", "RequiredPermissions": ["Permissions.Roles.AssignPermissions"]}]}, {"Id": "product-management", "Component": "BasicLayout", "Name": "ProductManagement", "Path": "/product", "Redirect": "/product/list", "Order": 2, "Title": "商品管理", "Icon": "ShoppingOutlined", "RequiredPermissions": ["Permissions.Menus.ProductManagement"], "Children": [{"Id": "product-list", "Name": "ProductList", "Path": "/product/list", "Component": "/product/list/index", "Title": "商品列表", "RequiredPermissions": ["Permissions.Products.View"]}, {"Id": "category-list", "Name": "CategoryList", "Path": "/product/category", "Component": "/product/category/index", "Title": "分类管理", "RequiredPermissions": ["Permissions.Categories.View"]}, {"Id": "attribute-list", "Name": "AttributeList", "Path": "/product/attribute", "Component": "/product/attribute/index", "Title": "属性管理", "RequiredPermissions": ["Permissions.Attributes.View"]}, {"Id": "pending-products", "Name": "PendingProducts", "Path": "/product/pending", "Component": "/product/pending/index", "Title": "待审核商品", "RequiredPermissions": ["Permissions.Products.Manage"]}]}, {"Id": "transaction-management", "Component": "BasicLayout", "Name": "TransactionManagement", "Path": "/transaction", "Redirect": "/transaction/order", "Order": 3, "Title": "交易管理", "Icon": "OrderedListOutlined", "RequiredPermissions": ["Permissions.Menus.OrderManagement", "Permissions.Menus.CartManagement"], "Children": [{"Id": "order-list", "Name": "OrderList", "Path": "/transaction/order", "Component": "/order/list/index", "Title": "订单列表", "RequiredPermissions": ["Permissions.Orders.View"]}, {"Id": "cart-list", "Name": "CartList", "Path": "/transaction/cart", "Component": "/cart/list/index", "Title": "购物车列表", "RequiredPermissions": ["Permissions.Carts.View"]}, {"Id": "abandoned-carts", "Name": "AbandonedCarts", "Path": "/transaction/abandoned", "Component": "/cart/abandoned/index", "Title": "未结购物车", "RequiredPermissions": ["Permissions.Carts.View"]}]}, {"Id": "inventory-logistics-management", "Component": "BasicLayout", "Name": "InventoryLogisticsManagement", "Path": "/inventory-logistics", "Redirect": "/inventory-logistics/inventory", "Order": 4, "Title": "库存与物流", "Icon": "InboxOutlined", "RequiredPermissions": ["Permissions.Menus.InventoryManagement", "Permissions.Menus.ShippingManagement"], "Children": [{"Id": "inventory-list", "Name": "InventoryList", "Path": "/inventory-logistics/inventory", "Component": "/inventory/list/index", "Title": "库存列表", "RequiredPermissions": ["Permissions.Inventory.View"]}, {"Id": "inventory-alerts", "Name": "InventoryAlerts", "Path": "/inventory-logistics/alerts", "Component": "/inventory/alerts/index", "Title": "库存预警", "RequiredPermissions": ["Permissions.Inventory.View"]}, {"Id": "shipping-list", "Name": "ShippingList", "Path": "/inventory-logistics/shipping", "Component": "/shipping/list/index", "Title": "物流列表", "RequiredPermissions": ["Permissions.Shipping.View"]}, {"Id": "shipping-area", "Name": "ShippingArea", "Path": "/inventory-logistics/area", "Component": "/shipping/area/index", "Title": "配送区域", "RequiredPermissions": ["Permissions.Shipping.View"]}, {"Id": "shipping-rules", "Name": "ShippingRules", "Path": "/inventory-logistics/rules", "Component": "/shipping/rules/index", "Title": "配送规则", "RequiredPermissions": ["Permissions.Shipping.View"]}]}, {"Id": "finance-management", "Component": "BasicLayout", "Name": "FinanceManagement", "Path": "/finance", "Redirect": "/finance/payment", "Order": 5, "Title": "财务管理", "Icon": "DollarOutlined", "RequiredPermissions": ["Permissions.Menus.PaymentManagement", "Permissions.Menus.InvoiceManagement", "Permissions.Menus.TaxManagement"], "Children": [{"Id": "payment-list", "Name": "PaymentList", "Path": "/finance/payment", "Component": "/payment/list/index", "Title": "支付列表", "RequiredPermissions": ["Permissions.Payments.View"]}, {"Id": "refund-list", "Name": "RefundList", "Path": "/finance/refund", "Component": "/payment/refund/index", "Title": "退款管理", "RequiredPermissions": ["Permissions.Payments.Refund"]}, {"Id": "invoice-list", "Name": "InvoiceList", "Path": "/finance/invoice", "Component": "/invoice/list/index", "Title": "发票列表", "RequiredPermissions": ["Permissions.Invoices.View"]}, {"Id": "tax-list", "Name": "TaxList", "Path": "/finance/tax", "Component": "/tax/list/index", "Title": "税务设置", "RequiredPermissions": ["Permissions.Tax.View"]}]}, {"Id": "marketing-management", "Component": "BasicLayout", "Name": "MarketingManagement", "Path": "/marketing", "Redirect": "/marketing/coupon", "Order": 6, "Title": "营销中心", "Icon": "GiftOutlined", "RequiredPermissions": ["Permissions.Menus.MarketingManagement"], "Children": [{"Id": "coupon-list", "Name": "CouponList", "Path": "/marketing/coupon", "Component": "/marketing/coupon/index", "Title": "优惠券管理", "RequiredPermissions": ["Permissions.Coupons.View"]}, {"Id": "promotion-list", "Name": "PromotionList", "Path": "/marketing/promotion", "Component": "/marketing/promotion/index", "Title": "促销活动", "RequiredPermissions": ["Permissions.Promotions.View"]}, {"Id": "marketing-contents", "Name": "MarketingContents", "Path": "/marketing/contents", "Component": "/marketing/contents/index", "Title": "营销内容", "RequiredPermissions": ["Permissions.MarketingContents.View"]}, {"Id": "user-coupons", "Name": "UserCoupons", "Path": "/marketing/user-coupons", "Component": "/marketing/user-coupons/index", "Title": "用户优惠券", "RequiredPermissions": ["Permissions.Coupons.View"]}]}, {"Id": "merchant-management", "Component": "BasicLayout", "Name": "MerchantManagement", "Path": "/merchant", "Redirect": "/merchant/list", "Order": 7, "Title": "商户管理", "Icon": "ShopOutlined", "RequiredPermissions": ["Permissions.Menus.MerchantManagement"], "Children": [{"Id": "merchant-list", "Name": "MerchantList", "Path": "/merchant/list", "Component": "/merchant/list/index", "Title": "商户列表", "RequiredPermissions": ["Permissions.Merchants.View"]}]}, {"Id": "after-sales-service", "Component": "BasicLayout", "Name": "AfterSalesService", "Path": "/after-sales", "Redirect": "/after-sales/return", "Order": 8, "Title": "售后服务", "Icon": "CustomerServiceOutlined", "RequiredPermissions": ["Permissions.Menus.ReturnManagement", "Permissions.Menus.ReviewManagement", "Permissions.Menus.TicketManagement"], "Children": [{"Id": "return-list", "Name": "ReturnList", "Path": "/after-sales/return", "Component": "/return/list/index", "Title": "退货列表", "RequiredPermissions": ["Permissions.Returns.View"]}, {"Id": "review-list", "Name": "ReviewList", "Path": "/after-sales/review", "Component": "/review/list/index", "Title": "评价列表", "RequiredPermissions": ["Permissions.Reviews.View"]}, {"Id": "ticket-list", "Name": "TicketList", "Path": "/after-sales/ticket", "Component": "/ticket/list/index", "Title": "工单列表", "RequiredPermissions": ["Permissions.Tickets.View"]}, {"Id": "ticket-category", "Name": "TicketCategory", "Path": "/after-sales/category", "Component": "/ticket/category/index", "Title": "工单分类", "RequiredPermissions": ["Permissions.Tickets.ManageCategories"]}]}, {"Id": "data-analysis", "Component": "BasicLayout", "Name": "DataAnalysis", "Path": "/data", "Redirect": "/data/sales", "Order": 9, "Title": "数据分析", "Icon": "BarChartOutlined", "RequiredPermissions": ["Permissions.Menus.ReportManagement"], "Children": [{"Id": "sales-overview", "Name": "SalesOverview", "Path": "/data/sales", "Component": "/report/sales/index", "Title": "销售概览", "RequiredPermissions": ["Permissions.Reports.ViewBasic"]}, {"Id": "inventory-overview", "Name": "InventoryOverview", "Path": "/data/inventory", "Component": "/report/inventory/index", "Title": "库存概览", "RequiredPermissions": ["Permissions.Reports.ViewBasic"]}, {"Id": "user-statistics", "Name": "UserStatistics", "Path": "/data/user", "Component": "/report/user/index", "Title": "用户统计", "RequiredPermissions": ["Permissions.Reports.ViewBasic"]}, {"Id": "top-selling", "Name": "TopSelling", "Path": "/data/topselling", "Component": "/report/topselling/index", "Title": "热销商品", "RequiredPermissions": ["Permissions.Reports.ViewBasic"]}]}, {"Id": "system-management", "Component": "BasicLayout", "Name": "SystemManagement", "Path": "/system", "Redirect": "/system/settings", "Order": 10, "Title": "系统管理", "Icon": "SettingOutlined", "RequiredPermissions": ["Permissions.<PERSON>us<PERSON>s", "Permissions.Menus.NotificationManagement", "Permissions.Menus.SystemIntegration", "Permissions.Menus.CacheManagement", "Permissions.Menus.MetricsManagement"], "Children": [{"Id": "system-settings", "Name": "SystemSettings", "Path": "/system/settings", "Component": "/settings/system/index", "Title": "系统设置", "RequiredPermissions": ["Permissions.Settings.View"]}, {"Id": "cache-management", "Name": "CacheManagement", "Path": "/system/cache", "Component": "/settings/cache/index", "Title": "缓存管理", "RequiredPermissions": ["Permissions.Cache.View"]}, {"Id": "log-management", "Name": "LogManagement", "Path": "/system/logs", "Component": "/settings/logs/index", "Title": "日志管理", "RequiredPermissions": ["Permissions.Settings.ViewLogs"]}, {"Id": "api-metrics", "Name": "ApiMetrics", "Path": "/system/api-metrics", "Component": "/settings/api-metrics/index", "Title": "API指标监控", "RequiredPermissions": ["Permissions.Metrics.ViewMetrics"]}, {"Id": "health-check", "Name": "HealthCheck", "Path": "/system/health-check", "Component": "/settings/health-check/index", "Title": "健康检查", "RequiredPermissions": ["Permissions.Metrics.ViewMetrics"]}, {"Id": "translation-management", "Name": "TranslationManagement", "Path": "/system/translations", "Component": "/settings/translations/index", "Title": "翻译管理", "RequiredPermissions": ["Permissions.Settings.Manage"]}, {"Id": "search-management", "Name": "SearchManagement", "Path": "/system/search", "Component": "/settings/search/index", "Title": "搜索配置", "RequiredPermissions": ["Permissions.Settings.Manage"]}, {"Id": "notification-list", "Name": "NotificationList", "Path": "/system/notification", "Component": "/notification/list/index", "Title": "通知列表", "RequiredPermissions": ["Permissions.Notifications.View"]}, {"Id": "notification-settings", "Name": "NotificationSettings", "Path": "/system/notification-settings", "Component": "/notification/settings/index", "Title": "通知设置", "RequiredPermissions": ["Permissions.Notifications.Manage"]}, {"Id": "webhook-management", "Name": "WebhookManagement", "Path": "/system/webhook", "Component": "/integration/webhook/index", "Title": "Webhook管理", "RequiredPermissions": ["Permissions.Integration.Manage"]}, {"Id": "social-login", "Name": "SocialLogin", "Path": "/system/social-login", "Component": "/integration/social-login/index", "Title": "社交登录配置", "RequiredPermissions": ["Permissions.Integration.Manage"]}]}]}