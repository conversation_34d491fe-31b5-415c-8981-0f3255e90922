using System;

namespace Ecommerce.Application.Features.Identity.Queries.GetUser;

/// <summary>
/// 获取用户信息响应
/// </summary>
public class GetUserResponse
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// 电子邮箱
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// 手机号码
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// 姓氏
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 上次登录时间
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 用户角色
    /// </summary>
    public string[] Roles { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 用户是否激活
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 邮箱是否已确认
    /// </summary>
    public bool IsEmailConfirmed { get; set; }

    /// <summary>
    /// 手机号是否已确认
    /// </summary>
    public bool IsPhoneConfirmed { get; set; }

    /// <summary>
    /// 用户状态
    /// </summary>
    public string Status { get; set; } = default!;

    /// <summary>
    /// 是否为系统用户（非Customer用户）
    /// </summary>
    public bool IsSystemUser { get; set; }

    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime? DeletedAt { get; set; }
}