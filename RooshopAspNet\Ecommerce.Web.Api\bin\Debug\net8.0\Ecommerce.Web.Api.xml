<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ecommerce.Web.Api</name>
    </assembly>
    <members>
        <member name="T:Ecommerce.Web.Api.Configurations.ApiVersioningConfiguration">
            <summary>
            API版本控制配置
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ApiVersioningConfiguration.AddApiVersioningConfiguration(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加API版本控制
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ApiVersioningConfiguration.AddSwaggerConfiguration(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加Swagger配置
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Configurations.ConfigureSwaggerOptions">
            <summary>
            配置Swagger选项，根据API版本生成文档
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ConfigureSwaggerOptions.#ctor(Microsoft.AspNetCore.Mvc.ApiExplorer.IApiVersionDescriptionProvider)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ConfigureSwaggerOptions.Configure(System.String,Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions)">
            <summary>
            配置命名选项
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ConfigureSwaggerOptions.Configure(Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions)">
            <summary>
            配置所有选项
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.ConfigureSwaggerOptions.CreateVersionInfo(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiVersionDescription)">
            <summary>
            创建版本信息
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Configurations.SecurityConfiguration">
            <summary>
            安全相关配置
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.SecurityConfiguration.AddCorsPolicies(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加CORS策略
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.SecurityConfiguration.AddRateLimiting(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加请求限流服务
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.SecurityConfiguration.AddCsrfProtection(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            添加CSRF防护
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Configurations.JwtClientResolveContributor">
            <summary>
            基于JWT的客户端ID解析器，从JWT中提取用户ID作为客户端标识
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Configurations.JwtClientResolveContributor.ResolveClientAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            解析客户端标识
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.AccountsApiController">
            <summary>
            账户API控制器 - 提供用户认证、注册、密码管理等功能
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.Register(Ecommerce.Application.Features.Identity.Commands.RegisterUser.RegisterUserCommand)">
            <summary>
            用户注册 - 创建新用户账户
            </summary>
            <param name="command">注册信息</param>
            <returns>注册结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.Login(Ecommerce.Application.Features.Identity.Commands.Login.LoginCommand)">
            <summary>
            用户登录 - 获取访问令牌
            </summary>
            <param name="command">登录信息</param>
            <returns>登录结果（含令牌）</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.RefreshToken(Ecommerce.Application.Features.Identity.Commands.RefreshToken.RefreshTokenCommand)">
            <summary>
            刷新访问令牌
            </summary>
            <param name="command">刷新令牌信息</param>
            <returns>新的访问令牌和刷新令牌</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.Logout(Ecommerce.Web.Api.Controllers.V1.LogoutRequest)">
            <summary>
            登出（销毁刷新令牌）
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ConfirmEmail(System.Guid,System.String,System.String)">
            <summary>
            确认邮箱
            </summary>
            <param name="userId">用户ID</param>
            <param name="token">确认令牌</param>
            <param name="expires">过期时间</param>
            <returns>确认结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ResendEmailConfirmation(Ecommerce.Application.Features.Identity.Commands.ResendEmailConfirmation.ResendEmailConfirmationCommand)">
            <summary>
            重新发送邮箱确认邮件
            </summary>
            <param name="command">重新发送邮箱确认邮件命令</param>
            <returns>发送结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.VerifyEmailWithCode(Ecommerce.Application.Features.Identity.Commands.VerifyEmailWithCode.VerifyEmailWithCodeCommand)">
            <summary>
            使用验证码验证邮箱
            </summary>
            <param name="command">验证码验证命令</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ChangePassword(Ecommerce.Application.Features.Identity.Commands.ChangePassword.ChangePasswordCommand)">
            <summary>
            修改密码
            </summary>
            <param name="command">修改密码命令</param>
            <returns>修改结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ForgotPassword(Ecommerce.Application.Features.Identity.Commands.ForgotPassword.ForgotPasswordCommand)">
            <summary>
            忘记密码 - 发送密码重置邮件
            </summary>
            <param name="command">忘记密码命令</param>
            <returns>发送结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ResetPassword(Ecommerce.Application.Features.Identity.Commands.ResetPassword.ResetPasswordCommand)">
            <summary>
            重置密码 - 使用重置令牌
            </summary>
            <param name="command">重置密码命令</param>
            <returns>重置结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AccountsApiController.ResetPasswordWithCode(Ecommerce.Application.Features.Identity.Commands.ResetPasswordWithCode.ResetPasswordWithCodeCommand)">
            <summary>
            使用验证码重置密码 - 更简单的重置方式
            </summary>
            <param name="command">使用验证码重置密码命令</param>
            <returns>重置结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.LogoutRequest">
            <summary>
            登出请求参数类
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.LogoutRequest.UserId">
            <summary>
            用户ID - 当令牌失效时可用于登出操作
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ApiMetricsController.GetAllMetrics">
            <summary>
            获取所有API端点的性能指标
            </summary>
            <returns>各端点的性能指标报告</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ApiMetricsController.GetMetricsForEndpoint(System.String)">
            <summary>
            获取特定API端点的性能指标
            </summary>
            <param name="endpointName">端点名称</param>
            <returns>端点的性能指标报告</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ApiMetricsController.ResetMetricsForEndpoint(System.String)">
            <summary>
            重置特定端点的性能指标
            </summary>
            <param name="endpointName">端点名称</param>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.AttributesApiController">
            <summary>
            商品属性API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.AttributesApiController})">
            <summary>
            初始化控制器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.GetAllAttributes">
            <summary>
            获取所有属性
            </summary>
            <returns>属性列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.GetAttributeById(System.Guid)">
            <summary>
            根据ID获取属性
            </summary>
            <param name="id">属性ID</param>
            <returns>属性信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.GetAttributeByCode(System.String)">
            <summary>
            根据代码获取属性
            </summary>
            <param name="code">属性代码</param>
            <returns>属性信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.CreateAttribute(Ecommerce.Application.Features.Products.Commands.CreateAttributeCommand)">
            <summary>
            创建属性
            </summary>
            <param name="command">创建属性命令</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.UpdateAttribute(System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateAttributeCommand)">
            <summary>
            更新属性
            </summary>
            <param name="id">属性ID</param>
            <param name="command">更新属性命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.DeleteAttribute(System.Guid)">
            <summary>
            删除属性
            </summary>
            <param name="id">属性ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.AddAttributeOption(System.Guid,Ecommerce.Application.Features.Products.Commands.AddAttributeOptionCommand)">
            <summary>
            添加属性选项
            </summary>
            <param name="attributeId">属性ID</param>
            <param name="command">添加属性选项命令</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.UpdateAttributeOption(System.Guid,System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateAttributeOptionCommand)">
            <summary>
            更新属性选项
            </summary>
            <param name="attributeId">属性ID</param>
            <param name="optionId">选项ID</param>
            <param name="command">更新属性选项命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.AttributesApiController.DeleteAttributeOption(System.Guid,System.Guid)">
            <summary>
            删除属性选项
            </summary>
            <param name="attributeId">属性ID</param>
            <param name="optionId">选项ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.BaseApiController">
            <summary>
            API控制器基类 - 提供统一响应格式和常用辅助方法
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.BaseApiController.Mediator">
            <summary>
            中介者实例，用于CQRS模式
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.BaseApiController.Logger">
            <summary>
            日志实例
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiSuccess``1(``0,System.String,System.Int32)">
            <summary>
            返回成功响应
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="message">消息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiSuccess(System.String,System.Int32)">
            <summary>
            返回成功响应（无数据）
            </summary>
            <param name="message">消息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiCreated``1(``0,System.String,System.Object,System.String)">
            <summary>
            返回创建成功响应
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="actionName">用于生成资源URL的Action名称</param>
            <param name="routeValues">路由值</param>
            <param name="message">消息</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiPagedSuccess``1(System.Collections.Generic.IEnumerable{``0},System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            返回分页成功响应
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="items">分页数据</param>
            <param name="totalCount">总记录数</param>
            <param name="currentPage">当前页码</param>
            <param name="pageSize">每页大小</param>
            <param name="message">消息</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiFail``1(System.String,System.String[],System.Int32)">
            <summary>
            返回失败响应
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="message">错误消息</param>
            <param name="errors">详细错误信息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiFail(System.String,System.String[],System.Int32)">
            <summary>
            返回失败响应（无数据类型）
            </summary>
            <param name="message">错误消息</param>
            <param name="errors">详细错误信息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiFail(System.String,System.String,System.Int32)">
            <summary>
            返回失败响应（单个错误）
            </summary>
            <param name="message">错误消息</param>
            <param name="error">单个错误信息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiNotFound(System.String,System.Object)">
            <summary>
            返回资源不存在响应
            </summary>
            <param name="resourceName">资源名称</param>
            <param name="id">资源ID</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiNotFound(System.String)">
            <summary>
            返回资源不存在响应（自定义消息）
            </summary>
            <param name="message">错误消息</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiValidationFail(System.Collections.Generic.Dictionary{System.String,System.String[]})">
            <summary>
            返回验证失败响应
            </summary>
            <param name="errors">验证错误信息</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.ApiForbidden(System.String)">
            <summary>
            返回无权限响应
            </summary>
            <param name="permission">所需权限</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.HandleException(System.Exception,System.String)">
            <summary>
            处理异常并返回适当的响应
            </summary>
            <param name="ex">异常</param>
            <param name="operation">操作描述</param>
            <returns>ActionResult</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.UserHasPermission(System.String)">
            <summary>
            检查当前用户是否有指定权限
            </summary>
            <param name="permission">权限名称</param>
            <returns>是否有权限</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.CheckPermission(System.String)">
            <summary>
            检查当前用户是否有指定权限，如果没有则返回禁止访问响应
            </summary>
            <param name="permission">权限名称</param>
            <returns>null表示有权限，否则返回禁止访问响应</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.GetRequestLocale(System.String)">
            <summary>
            获取当前请求的语言
            </summary>
            <param name="queryLocale">查询参数中的语言代码</param>
            <returns>语言代码</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.GetCurrentUserId">
            <summary>
            获取当前用户ID
            </summary>
            <returns>用户ID字符串，如果未找到则返回null</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.BaseApiController.GetCurrentUserGuid">
            <summary>
            获取当前用户ID并转换为Guid
            </summary>
            <returns>用户ID的Guid形式，如果未找到或格式不正确则返回Guid.Empty</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.CacheManagementController">
            <summary>
            缓存管理控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.GetStatistics">
            <summary>
            获取缓存统计信息
            </summary>
            <returns>缓存统计信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.GetHotKeys(System.Int32)">
            <summary>
            获取热门缓存键
            </summary>
            <param name="count">返回数量</param>
            <returns>热门缓存键列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.ResetStatistics">
            <summary>
            重置缓存统计信息
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.ClearCache(Ecommerce.Application.Features.Cache.Commands.ClearCache.ClearCacheCommand)">
            <summary>
            清除缓存
            </summary>
            <param name="command">清除缓存命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.WarmupCache">
            <summary>
            预热缓存
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.WarmupCacheAsync">
            <summary>
            异步预热缓存
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CacheManagementController.RefreshCache(System.String)">
            <summary>
            刷新缓存
            </summary>
            <param name="key">缓存键</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.CartApiController">
            <summary>
            购物车API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.#ctor">
            <summary>
            初始化购物车API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.GetCart">
            <summary>
            获取当前用户的购物车
            </summary>
            <returns>购物车</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.AddItemToCart(Ecommerce.Application.Features.Carts.Commands.AddItemToCartCommand)">
            <summary>
            添加商品到购物车
            </summary>
            <param name="command">添加商品命令</param>
            <returns>添加的购物车项目</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.UpdateCartItem(System.Guid,Ecommerce.Application.Features.Carts.Commands.UpdateCartItemCommand)">
            <summary>
            更新购物车项目数量
            </summary>
            <param name="itemId">购物车项目ID</param>
            <param name="command">更新购物车项目命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.RemoveCartItem(System.Guid)">
            <summary>
            移除购物车项目
            </summary>
            <param name="itemId">购物车项目ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.ClearCart">
            <summary>
            清空购物车
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.CheckoutCart(Ecommerce.Application.Features.Carts.Commands.CheckoutCartCommand)">
            <summary>
            结算购物车
            </summary>
            <param name="command">结算购物车命令</param>
            <returns>结算结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CartApiController.GetUserId">
            <summary>
            获取当前用户ID
            </summary>
            <returns>用户ID</returns>
            <exception cref="T:System.UnauthorizedAccessException">无法获取用户ID时抛出</exception>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController">
            <summary>
            商品分类API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.CategoriesApiController})">
            <summary>
            初始化控制器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.SearchCategories(System.String,System.Nullable{System.Guid},System.Int32,System.Int32,System.String)">
            <summary>
            搜索分类 (分页)
            </summary>
            <param name="keyword">关键词</param>
            <param name="parentId">父分类ID</param>
            <param name="page">页码</param>
            <param name="pageSize">每页记录数</param>
            <param name="locale">语言代码</param>
            <returns>分类分页结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.GetCategorySuggestions(System.String,System.Int32,System.String)">
            <summary>
            获取分类搜索建议
            </summary>
            <param name="keyword">关键词</param>
            <param name="maxResults">最大返回数量</param>
            <param name="locale">语言代码</param>
            <returns>搜索建议列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.GetCategoryTree">
            <summary>
            获取分类树
            </summary>
            <returns>分类树</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.GetCategoryById(System.Guid,System.String)">
            <summary>
            根据ID获取分类
            </summary>
            <param name="id">分类ID</param>
            <param name="locale">语言代码</param>
            <returns>分类信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.GetCategoryBySlug(System.String,System.String)">
            <summary>
            根据Slug获取分类
            </summary>
            <param name="slug">分类Slug</param>
            <param name="locale">语言代码 (用于翻译)</param>
            <returns>分类信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.CreateCategory(Ecommerce.Application.Features.Products.Commands.CreateCategoryCommand)">
            <summary>
            创建分类
            </summary>
            <param name="command">创建分类命令</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.UpdateCategory(System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateCategoryCommand)">
            <summary>
            更新分类
            </summary>
            <param name="id">分类ID</param>
            <param name="command">更新分类命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.MoveCategory(System.Guid,Ecommerce.Application.Features.Products.Commands.MoveCategoryCommand)">
            <summary>
            移动分类
            </summary>
            <param name="id">要移动的分类ID</param>
            <param name="command">包含目标父分类ID的命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CategoriesApiController.DeleteCategory(System.Guid)">
            <summary>
            删除分类
            </summary>
            <param name="id">分类ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.CouponsApiController">
            <summary>
            优惠券API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.CouponsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.SearchCoupons(System.String,System.Nullable{System.Int32},System.Int32,System.Int32,System.String)">
            <summary>
            搜索优惠券 (分页)
            </summary>
            <param name="keyword">关键词</param>
            <param name="status">优惠券状态 (0: Inactive, 1: Active, 2: Expired, 3: Used)</param>
            <param name="page">页码</param>
            <param name="pageSize">每页大小</param>
            <param name="locale">语言代码</param>
            <returns>优惠券分页结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.GetCouponSuggestions(System.String,System.Int32,System.String)">
            <summary>
            获取优惠券搜索建议
            </summary>
            <param name="keyword">关键词 (至少2个字符)</param>
            <param name="maxResults">最大返回数量 (默认10, 最大50)</param>
            <param name="locale">语言代码</param>
            <returns>搜索建议列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.GetCoupons(System.Nullable{System.Int32},System.Int32,System.Int32)">
            <summary>
            获取优惠券列表 (管理员)
            </summary>
            <param name="status">优惠券状态 (0: Inactive, 1: Active, 2: Expired, 3: Used)</param>
            <param name="skip">跳过数量</param>
            <param name="take">获取数量 (默认20, 最大100)</param>
            <returns>优惠券列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.GetCouponById(System.Guid,System.String)">
            <summary>
            根据ID获取优惠券 (管理员)
            </summary>
            <param name="id">优惠券ID</param>
            <param name="locale">语言代码</param>
            <returns>优惠券</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.GetCouponByCode(System.String,System.String)">
            <summary>
            根据代码获取优惠券 (公共)
            </summary>
            <param name="code">优惠券代码</param>
            <param name="locale">语言代码</param>
            <returns>优惠券</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.CreateCoupon(Ecommerce.Application.Features.Marketing.Commands.CreateCouponCommand)">
            <summary>
            创建优惠券 (管理员)
            </summary>
            <param name="command">创建优惠券命令</param>
            <returns>创建的优惠券ID</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.UpdateCoupon(System.Guid,Ecommerce.Application.Features.Marketing.Commands.UpdateCouponCommand)">
            <summary>
            更新优惠券 (管理员)
            </summary>
            <param name="id">优惠券ID</param>
            <param name="command">更新优惠券命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.DisableCoupon(System.Guid)">
            <summary>
            禁用优惠券 (管理员)
            </summary>
            <param name="id">优惠券ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.EnableCoupon(System.Guid)">
            <summary>
            启用优惠券 (管理员)
            </summary>
            <param name="id">优惠券ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.CouponsApiController.ValidateCoupon(Ecommerce.Application.Features.Marketing.Queries.ValidateCouponQuery)">
            <summary>
            验证优惠券代码 (公共)
            </summary>
            <param name="query">验证查询</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.HealthCheckApiController.Get">
            <summary>
            获取系统健康状态
            </summary>
            <returns>系统各组件健康状态报告</returns>
            <response code="200">系统健康</response>
            <response code="503">系统存在不健康的组件</response>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.InventoryApiController">
            <summary>
            库存管理API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.InventoryApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.GetById(System.Guid)">
            <summary>
            获取库存项
            </summary>
            <param name="id">库存项ID</param>
            <returns>库存项</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.GetBySku(System.String)">
            <summary>
            根据SKU获取库存项
            </summary>
            <param name="sku">商品SKU</param>
            <returns>库存项</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.Create(Ecommerce.Application.Features.Inventory.Commands.CreateInventoryItemCommand)">
            <summary>
            创建库存项
            </summary>
            <param name="command">创建库存项命令</param>
            <returns>创建的库存项</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.UpdateQuantity(Ecommerce.Application.Features.Inventory.Commands.UpdateInventoryQuantityCommand)">
            <summary>
            更新库存数量
            </summary>
            <param name="command">更新库存数量命令</param>
            <returns>更新后的库存项</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.Reserve(Ecommerce.Application.Features.Inventory.Commands.ReserveInventoryCommand)">
            <summary>
            预留库存
            </summary>
            <param name="command">预留库存命令</param>
            <returns>预留操作结果 (包含锁定ID)</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.ConfirmReservation(Ecommerce.Application.Features.Inventory.Commands.ConfirmInventoryReservationCommand)">
            <summary>
            确认库存预留
            </summary>
            <param name="command">确认库存预留命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.CancelReservation(Ecommerce.Application.Features.Inventory.Commands.CancelInventoryReservationCommand)">
            <summary>
            取消库存预留
            </summary>
            <param name="command">取消库存预留命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InventoryApiController.CleanExpiredLocks">
            <summary>
            清理过期的库存锁定
            </summary>
            <returns>清理的锁定数量</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController">
            <summary>
            发票API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController.#ctor(Ecommerce.Application.Interfaces.External.IInvoiceService)">
            <summary>
            构造函数
            </summary>
            <param name="invoiceService">发票服务</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController.GenerateInvoice(Ecommerce.Application.Features.Invoices.Commands.GenerateInvoiceCommand)">
            <summary>
            为订单生成发票
            </summary>
            <param name="command">生成发票命令</param>
            <returns>发票DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController.GetInvoice(System.Guid)">
            <summary>
            获取发票详情
            </summary>
            <param name="id">发票ID</param>
            <returns>发票DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController.DownloadInvoicePdf(System.Guid)">
            <summary>
            下载发票PDF
            </summary>
            <param name="id">发票ID</param>
            <returns>PDF文件</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.InvoicesApiController.SendInvoiceEmail(System.Guid,System.String)">
            <summary>
            发送发票邮件
            </summary>
            <param name="id">发票ID</param>
            <param name="email">收件人邮箱（可选, 默认使用订单关联邮箱）</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController">
            <summary>
            营销内容API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.GetMarketingContents(Ecommerce.Application.Features.Marketing.Queries.SearchMarketingContentsQuery)">
            <summary>
            获取营销内容列表 (管理员)
            </summary>
            <param name="query">搜索查询</param>
            <returns>营销内容分页列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.GetMarketingContentById(System.Guid)">
            <summary>
            根据ID获取营销内容 (管理员)
            </summary>
            <param name="id">营销内容ID</param>
            <returns>营销内容</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.GetMarketingContentByCode(System.String)">
            <summary>
            根据代码获取营销内容 (公开)
            </summary>
            <param name="code">营销内容代码</param>
            <returns>营销内容</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.GetActiveMarketingContentsByType(System.Int32,System.String)">
            <summary>
            获取指定类型的活跃营销内容 (公开)
            </summary>
            <param name="contentType">内容类型</param>
            <param name="position">位置标识（可选）</param>
            <returns>营销内容列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.CreateMarketingContent(Ecommerce.Application.Features.Marketing.Commands.CreateMarketingContentCommand)">
            <summary>
            创建营销内容 (管理员)
            </summary>
            <param name="command">创建营销内容命令</param>
            <returns>创建的营销内容ID</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.UpdateMarketingContent(System.Guid,Ecommerce.Application.Features.Marketing.Commands.UpdateMarketingContentCommand)">
            <summary>
            更新营销内容 (管理员)
            </summary>
            <param name="id">营销内容ID</param>
            <param name="command">更新营销内容命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.UpdateMarketingContentStatus(System.Guid,Ecommerce.Application.Features.Marketing.Commands.UpdateMarketingContentStatusCommand)">
            <summary>
            更新营销内容状态 (管理员)
            </summary>
            <param name="id">营销内容ID</param>
            <param name="command">更新营销内容状态命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MarketingContentsApiController.DeleteMarketingContent(System.Guid)">
            <summary>
            删除营销内容 (管理员)
            </summary>
            <param name="id">营销内容ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.MenuApiController">
            <summary>
            菜单API控制器 - 提供用户菜单数据
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MenuApiController.GetAllMenus">
            <summary>
            获取当前用户可访问的所有菜单
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController">
            <summary>
            商户API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.GetAllMerchants(System.Boolean)">
            <summary>
            获取所有商户 (需要管理员权限)
            </summary>
            <param name="forceRefresh">是否强制刷新缓存</param>
            <returns>商户列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.GetMerchantById(System.Guid,System.Boolean)">
            <summary>
            根据ID获取商户 (需要管理员权限)
            </summary>
            <param name="id">商户ID</param>
            <param name="forceRefresh">是否强制刷新缓存</param>
            <returns>商户</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.CreateMerchant(Ecommerce.Application.Features.Merchants.Commands.CreateMerchant.CreateMerchantCommand)">
            <summary>
            创建商户 (需要管理员权限)
            </summary>
            <param name="command">创建商户命令</param>
            <returns>创建的商户</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.UpdateMerchant(System.Guid,Ecommerce.Application.Features.Merchants.Commands.UpdateMerchant.UpdateMerchantCommand)">
            <summary>
            更新商户 (需要管理员权限)
            </summary>
            <param name="id">商户ID</param>
            <param name="command">更新商户命令</param>
            <returns>更新后的商户</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.SetMerchantApprovalRequirement(System.Guid,System.Boolean)">
            <summary>
            设置商户商品审核要求 (需要管理员权限)
            </summary>
            <param name="id">商户ID</param>
            <param name="requiresApproval">是否需要审核</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.MerchantsApiController.RefreshMerchantCache(System.Nullable{System.Guid})">
            <summary>
            强制刷新商户缓存 (需要管理员权限)
            </summary>
            <param name="id">商户ID，如果不提供则刷新所有商户缓存</param>
            <returns>刷新结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.NotificationsController">
            <summary>
            通知控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.#ctor(Ecommerce.Application.Interfaces.INotificationService,Ecommerce.Domain.Interfaces.INotificationRepository,Ecommerce.Application.Interfaces.Identity.ICurrentUserService,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.NotificationsController})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.GetUserNotifications(System.Nullable{Ecommerce.Domain.Aggregates.Settings.NotificationStatus},System.Int32,System.Int32)">
            <summary>
            获取当前用户的通知
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.GetUnreadCount">
            <summary>
            获取未读通知数量
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.MarkAsRead(System.Guid)">
            <summary>
            标记通知为已读
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.MarkAllAsRead">
            <summary>
            标记所有通知为已读
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.NotificationsController.SendAnnouncement(Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest)">
            <summary>
            发送系统公告 (仅管理员)
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest">
            <summary>
            发送公告请求
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.Title">
            <summary>
            公告标题
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.Content">
            <summary>
            公告内容
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.Importance">
            <summary>
            重要程度 (1=低, 2=中, 3=高)
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.TargetUserGroup">
            <summary>
            目标用户组 (可选)
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.IsGlobal">
            <summary>
            是否为全局公告
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.SendAnnouncementRequest.ExpiryDate">
            <summary>
            过期时间 (可选)
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.OrdersApiController">
            <summary>
            订单API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.OrdersApiController.GetUserOrders(System.Nullable{System.Int32},System.Int32,System.Int32)">
            <summary>
            获取当前用户的订单列表 (分页)
            </summary>
            <param name="status">订单状态 (可选)</param>
            <param name="pageIndex">页码 (从0开始)</param>
            <param name="pageSize">每页大小 (默认10)</param>
            <returns>订单列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.OrdersApiController.GetOrder(System.Guid)">
            <summary>
            获取订单详情
            </summary>
            <param name="id">订单ID</param>
            <returns>订单详情</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.OrdersApiController.CreateOrder(Ecommerce.Application.Features.Orders.Commands.CreateOrderCommand)">
            <summary>
            创建订单
            </summary>
            <param name="command">创建订单命令</param>
            <returns>创建的订单</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController">
            <summary>
            支付API控制器 - 提供支付方式管理和支付处理功能
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.GetPaymentMethods(System.Boolean,System.String)">
            <summary>
            获取支付方式列表 - 公开接口，无需权限
            </summary>
            <param name="activeOnly">是否只获取激活的支付方式</param>
            <param name="providerCode">服务提供商代码</param>
            <returns>支付方式DTO集合</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.GetPaymentMethodByCode(System.String)">
            <summary>
            根据代码获取支付方式 - 公开接口，无需权限
            </summary>
            <param name="code">支付方式代码</param>
            <returns>支付方式DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.InitiatePayment(Ecommerce.Application.Features.Payments.DTOs.InitiatePaymentDto)">
            <summary>
            初始化支付 - 需要创建订单权限
            </summary>
            <param name="request">初始化支付DTO</param>
            <returns>初始化支付结果DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.GetPaymentById(System.Guid)">
            <summary>
            根据ID获取支付 - 需要查看支付权限
            </summary>
            <param name="id">支付ID</param>
            <returns>支付DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.GetPaymentsByOrderId(System.Guid)">
            <summary>
            根据订单ID获取支付 - 需要查看订单权限
            </summary>
            <param name="orderId">订单ID</param>
            <returns>支付DTO集合</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.ProcessStripeWebhook">
            <summary>
            处理Stripe Webhook - 公开接口，无需权限
            </summary>
            <returns>Webhook结果DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PaymentsApiController.ProcessAirwallexWebhook">
            <summary>
            处理Airwallex Webhook - 公开接口，无需权限
            </summary>
            <returns>Webhook结果DTO</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController">
            <summary>
            权限组管理控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.#ctor(Ecommerce.Application.Interfaces.Identity.IPermissionGroupService,Ecommerce.Application.Interfaces.Identity.IPermissionProvider,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.GetAllPermissionGroups">
            <summary>
            获取所有权限组
            </summary>
            <returns>权限组列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.GetPermissionGroupById(System.Guid)">
            <summary>
            根据ID获取权限组
            </summary>
            <param name="id">权限组ID</param>
            <returns>权限组</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.CreatePermissionGroup(Ecommerce.Application.Features.Identity.Commands.PermissionGroups.CreatePermissionGroupCommand)">
            <summary>
            创建权限组
            </summary>
            <param name="request">创建权限组请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.UpdatePermissionGroup(System.Guid,Ecommerce.Application.Features.Identity.Commands.PermissionGroups.UpdatePermissionGroupCommand)">
            <summary>
            更新权限组
            </summary>
            <param name="id">权限组ID</param>
            <param name="request">更新权限组请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.DeletePermissionGroup(System.Guid)">
            <summary>
            删除权限组
            </summary>
            <param name="id">权限组ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.AddPermissionToGroup(System.Guid,System.String)">
            <summary>
            为权限组添加权限
            </summary>
            <param name="id">权限组ID</param>
            <param name="permission">权限名称</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.RemovePermissionFromGroup(System.Guid,System.String)">
            <summary>
            从权限组移除权限
            </summary>
            <param name="id">权限组ID</param>
            <param name="permission">权限名称</param>
            <returns>移除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.AssignGroupToRole(System.String,System.Guid)">
            <summary>
            为角色分配权限组
            </summary>
            <param name="roleId">角色ID</param>
            <param name="permissionGroupId">权限组ID</param>
            <returns>分配结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.RemoveGroupFromRole(System.String,System.Guid)">
            <summary>
            从角色移除权限组
            </summary>
            <param name="roleId">角色ID</param>
            <param name="permissionGroupId">权限组ID</param>
            <returns>移除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.GetRolePermissionGroups(System.String)">
            <summary>
            获取角色的权限组
            </summary>
            <param name="roleId">角色ID</param>
            <returns>权限组列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionGroupsApiController.GetAvailablePermissions">
            <summary>
            获取所有可用权限
            </summary>
            <returns>权限列表</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController">
            <summary>
            权限管理控制器 - 提供权限管理和用户权限查询功能
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.#ctor(Ecommerce.Application.Interfaces.Identity.IPermissionService,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.PermissionsApiController})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetAllPermissions">
            <summary>
            获取所有权限列表
            </summary>
            <returns>权限列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetRolePermissions(System.String)">
            <summary>
            获取角色权限
            </summary>
            <param name="roleId">角色ID</param>
            <returns>角色权限信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.UpdateRolePermissions(System.String,Ecommerce.Application.Features.Identity.Commands.Permissions.UpdateRolePermissionsCommand)">
            <summary>
            更新角色权限
            </summary>
            <param name="roleId">角色ID</param>
            <param name="command">更新角色权限命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.AddPermissionToRole(System.String,System.String)">
            <summary>
            为角色添加权限
            </summary>
            <param name="roleId">角色ID</param>
            <param name="permission">权限名称</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.RemovePermissionFromRole(System.String,System.String)">
            <summary>
            从角色移除权限
            </summary>
            <param name="roleId">角色ID</param>
            <param name="permission">权限名称</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.HasUserPermission(System.String)">
            <summary>
            检查当前用户是否拥有指定权限
            </summary>
            <param name="permission">权限名称</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetMyPermissions">
            <summary>
            获取用户的所有权限（包括继承权限）
            </summary>
            <returns>权限列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetPermissionMetadata">
            <summary>
            获取所有权限的元数据信息
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetCurrentUserPermissions">
            <summary>
            获取当前用户所有权限
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetUserPermissions(System.String)">
            <summary>
            获取指定用户的权限
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.GetCurrentUserPermissionCodes">
            <summary>
            获取当前用户的权限码列表（用于前端按钮级别权限控制）
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.ValidatePermission(System.String)">
            <summary>
            验证当前用户是否拥有特定权限
            </summary>
            <param name="permission">权限名称</param>
            <returns>是否拥有权限</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PermissionsApiController.DebugUserClaims">
            <summary>
            调试用 - 显示当前用户的所有声明和权限
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ProductsApiController">
            <summary>
            商品API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.ProductsApiController})">
            <summary>
            初始化控制器
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetProducts(System.String,System.Nullable{System.Guid},System.Nullable{System.Decimal},System.Nullable{System.Decimal},System.Boolean,System.Nullable{System.Guid},System.String,System.Nullable{System.Decimal},Ecommerce.Domain.Aggregates.Products.ProductSortOption,System.Boolean,System.Int32,System.Int32,System.String)">
            <summary>
            获取商品列表
            </summary>
            <param name="keyword">关键词</param>
            <param name="categoryId">分类ID</param>
            <param name="minPrice">最低价格</param>
            <param name="maxPrice">最高价格</param>
            <param name="onlyPublished">是否只获取已发布的商品</param>
            <param name="merchantId">商户ID</param>
            <param name="tags">标签列表（逗号分隔）</param>
            <param name="minRating">最低评分</param>
            <param name="sortBy">排序方式</param>
            <param name="useFuzzySearch">是否使用模糊搜索</param>
            <param name="page">页码</param>
            <param name="pageSize">每页记录数</param>
            <param name="locale">语言代码</param>
            <returns>商品列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetProductById(System.Guid,System.String)">
            <summary>
            根据ID获取商品
            </summary>
            <param name="id">商品ID</param>
            <param name="locale">语言代码</param>
            <returns>商品信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetProductBySlug(System.String,System.String)">
            <summary>
            根据Slug获取商品
            </summary>
            <param name="slug">商品Slug</param>
            <param name="locale">语言代码</param>
            <returns>商品信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.CreateProduct(Ecommerce.Application.Features.Products.Commands.CreateProductCommand)">
            <summary>
            创建商品 (需要管理员或商户权限)
            </summary>
            <param name="command">创建商品命令</param>
            <returns>创建结果 (商品ID)</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.UpdateProduct(System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateProductCommand)">
            <summary>
            更新商品 (需要管理员或商户权限)
            </summary>
            <param name="id">商品ID</param>
            <param name="command">更新商品命令</param>
            <returns>更新后的商品</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.DeleteProduct(System.Guid)">
            <summary>
            删除商品 (需要管理员权限)
            </summary>
            <param name="id">商品ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.AddProductVariant(System.Guid,Ecommerce.Application.Features.Products.Commands.AddProductVariantCommand)">
            <summary>
            添加商品变体 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="command">添加商品变体命令</param>
            <returns>创建的商品变体，或已存在的变体信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.AddProductImage(System.Guid,Ecommerce.Application.Features.Products.Commands.AddProductImageCommand)">
            <summary>
            添加商品图片 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="command">添加商品图片命令</param>
            <returns>创建的商品图片信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetProductVariants(System.Guid)">
            <summary>
            获取商品变体列表 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <returns>商品变体列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetProductImages(System.Guid)">
            <summary>
            获取商品图片列表 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <returns>商品图片列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.UpdateProductVariant(System.Guid,System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateProductVariantCommand)">
            <summary>
            更新商品变体 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="variantId">变体ID</param>
            <param name="command">更新商品变体命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.UpdateProductImage(System.Guid,System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateProductImageCommand)">
            <summary>
            更新商品图片 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="imageId">图片ID</param>
            <param name="command">更新商品图片命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.DeleteProductVariant(System.Guid,System.Guid)">
            <summary>
            删除商品变体 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="variantId">变体ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.DeleteProductImage(System.Guid,System.Guid)">
            <summary>
            删除商品图片 (需要管理员或商户权限)
            </summary>
            <param name="productId">商品ID</param>
            <param name="imageId">图片ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.GetPendingProducts(System.Nullable{System.Guid},System.String,System.Int32,System.Int32)">
            <summary>
            获取待审核商品列表 (需要管理员权限)
            </summary>
            <param name="merchantId">商户ID (可选)</param>
            <param name="keyword">关键词 (可选)</param>
            <param name="page">页码</param>
            <param name="pageSize">每页记录数</param>
            <returns>待审核商品分页列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.ApproveProduct(System.Guid,Ecommerce.Application.Features.Products.Commands.ApproveProduct.ApproveProductCommand)">
            <summary>
            批准商品 (需要管理员权限)
            </summary>
            <param name="id">商品ID</param>
            <param name="command">批准商品命令 (可包含备注)</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ProductsApiController.RejectProduct(System.Guid,Ecommerce.Application.Features.Products.Commands.RejectProduct.RejectProductCommand)">
            <summary>
            拒绝商品审核 (需要管理员权限)
            </summary>
            <param name="id">商品ID</param>
            <param name="command">拒绝商品命令 (包含原因)</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController">
            <summary>
            促销活动API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.PromotionsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.GetPromotions(System.Nullable{System.Int32},System.Int32,System.Int32,System.String)">
            <summary>
            获取促销活动列表 (管理员)
            </summary>
            <param name="status">促销活动状态</param>
            <param name="skip">跳过数量</param>
            <param name="take">获取数量</param>
            <param name="locale">语言代码</param>
            <returns>促销活动列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.SearchPromotions(System.String,System.Nullable{System.Int32},System.Int32,System.Int32,System.String)">
            <summary>
            搜索促销活动 (管理员)
            </summary>
            <param name="keyword">关键词</param>
            <param name="status">促销活动状态</param>
            <param name="page">页码</param>
            <param name="pageSize">每页记录数</param>
            <param name="locale">语言代码</param>
            <returns>促销活动搜索结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.GetValidPromotions(System.Nullable{System.Guid},System.Nullable{System.Guid},System.String)">
            <summary>
            获取有效的促销活动
            </summary>
            <param name="productId">商品ID</param>
            <param name="categoryId">类别ID</param>
            <param name="locale">语言代码</param>
            <returns>有效的促销活动列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.GetPromotionById(System.Guid,System.String)">
            <summary>
            根据ID获取促销活动
            </summary>
            <param name="id">促销活动ID</param>
            <param name="locale">语言代码</param>
            <returns>促销活动详情</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.CreatePromotion(Ecommerce.Application.Features.Marketing.Commands.CreatePromotionCommand)">
            <summary>
            创建促销活动 (管理员)
            </summary>
            <param name="command">创建促销活动命令</param>
            <returns>创建的促销活动ID</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.UpdatePromotion(System.Guid,Ecommerce.Application.Features.Marketing.Commands.UpdatePromotionCommand)">
            <summary>
            更新促销活动 (管理员)
            </summary>
            <param name="id">促销活动ID</param>
            <param name="command">更新促销活动命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.ActivatePromotion(System.Guid)">
            <summary>
            激活促销活动 (管理员)
            </summary>
            <param name="id">促销活动ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.DeactivatePromotion(System.Guid)">
            <summary>
            停用促销活动 (管理员)
            </summary>
            <param name="id">促销活动ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.PromotionsApiController.DeletePromotion(System.Guid)">
            <summary>
            删除促销活动 (管理员)
            </summary>
            <param name="id">促销活动ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ReportsApiController">
            <summary>
            报表API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetSalesOverview(Ecommerce.Application.Features.Reports.Queries.GetSalesOverview.GetSalesOverviewQuery)">
            <summary>
            获取销售概览统计
            </summary>
            <param name="query">销售概览查询参数</param>
            <returns>销售概览统计数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetSalesTrend(Ecommerce.Application.Features.Reports.Queries.GetSalesTrend.GetSalesTrendQuery)">
            <summary>
            获取销售趋势分析
            </summary>
            <param name="query">销售趋势查询参数</param>
            <returns>销售趋势分析数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetTopSellingProducts(Ecommerce.Application.Features.Reports.Queries.GetTopSellingProducts.GetTopSellingProductsQuery)">
            <summary>
            获取商品销售排行
            </summary>
            <param name="query">商品销售排行查询参数</param>
            <returns>商品销售排行数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetUserGrowthStats(Ecommerce.Application.Features.Reports.Queries.GetUserGrowthStats.GetUserGrowthStatsQuery)">
            <summary>
            获取用户增长统计
            </summary>
            <param name="query">用户增长统计查询参数</param>
            <returns>用户增长统计数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetUserPurchaseBehavior(Ecommerce.Application.Features.Reports.Queries.GetUserPurchaseBehavior.GetUserPurchaseBehaviorQuery)">
            <summary>
            获取用户购买行为分析
            </summary>
            <param name="query">用户购买行为分析查询参数</param>
            <returns>用户购买行为分析数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetUserSegmentation(Ecommerce.Application.Features.Reports.Queries.GetUserSegmentation.GetUserSegmentationQuery)">
            <summary>
            获取用户细分
            </summary>
            <param name="query">用户细分查询参数</param>
            <returns>用户细分数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetInventoryOverview(Ecommerce.Application.Features.Reports.Queries.GetInventoryOverview.GetInventoryOverviewQuery)">
            <summary>
            获取库存概览统计
            </summary>
            <param name="query">库存概览查询参数</param>
            <returns>库存概览统计数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetInventoryTurnoverRate(Ecommerce.Application.Features.Reports.Queries.GetInventoryTurnoverRate.GetInventoryTurnoverRateQuery)">
            <summary>
            获取库存周转率报表
            </summary>
            <param name="query">库存周转率查询参数</param>
            <returns>库存周转率报表数据</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReportsApiController.GetInventoryAlerts(Ecommerce.Application.Features.Reports.Queries.GetInventoryAlerts.GetInventoryAlertsQuery)">
            <summary>
            获取库存预警分析
            </summary>
            <param name="query">库存预警分析查询参数</param>
            <returns>库存预警分析数据</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController">
            <summary>
            退货API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.RequestReturn(Ecommerce.Application.Features.Returns.Commands.RequestReturn.RequestReturnCommand)">
            <summary>
            请求退货 (用户)
            </summary>
            <param name="command">请求退货命令</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.ApproveReturn(System.Guid,Ecommerce.Application.Features.Returns.Commands.ApproveReturn.ApproveReturnCommand)">
            <summary>
            批准退货请求 (管理员)
            </summary>
            <param name="id">退货请求ID</param>
            <param name="command">批准退货命令</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.RejectReturn(System.Guid,Ecommerce.Application.Features.Returns.Commands.RejectReturn.RejectReturnCommand)">
            <summary>
            拒绝退货请求 (管理员)
            </summary>
            <param name="id">退货请求ID</param>
            <param name="command">拒绝退货命令 (需要原因)</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.MarkItemsAsReceived(System.Guid,Ecommerce.Application.Features.Returns.Commands.MarkReturnItemsAsReceived.MarkReturnItemsAsReceivedCommand)">
            <summary>
            标记退货商品已接收 (管理员)
            </summary>
            <param name="id">退货请求ID</param>
            <param name="command">标记退货商品已接收命令</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.InitiateRefund(System.Guid,Ecommerce.Application.Features.Returns.Commands.InitiateRefundForReturn.InitiateRefundForReturnCommand)">
            <summary>
            启动退款 (管理员)
            </summary>
            <param name="id">退货请求ID</param>
            <param name="command">启动退款命令</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.GetReturnRequest(System.Guid)">
            <summary>
            获取退货请求详情 (用户/管理员)
            </summary>
            <param name="id">退货请求ID</param>
            <returns>退货请求DTO</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReturnsApiController.GetMyReturnRequests">
            <summary>
            获取当前用户的退货请求列表
            </summary>
            <returns>退货请求DTO列表</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController">
            <summary>
            评价API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.GetReviewsByProductId(System.Guid,System.String,System.Boolean,System.Boolean,System.Int32,System.Int32)">
            <summary>
            获取商品评价列表 (分页, 公开)
            </summary>
            <param name="productId">商品ID</param>
            <param name="status">状态 (可选: Pending, Published, Rejected, Deleted)</param>
            <param name="includeImages">是否包含图片</param>
            <param name="includeReplies">是否包含回复</param>
            <param name="page">页码</param>
            <param name="pageSize">每页数量</param>
            <returns>评价分页列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.GetReviewById(System.Guid,System.Boolean,System.Boolean)">
            <summary>
            获取评价详情 (公开)
            </summary>
            <param name="id">评价ID</param>
            <param name="includeImages">是否包含图片</param>
            <param name="includeReplies">是否包含回复</param>
            <returns>评价详情</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.GetProductRatingStats(System.Guid)">
            <summary>
            获取商品评分统计 (公开)
            </summary>
            <param name="productId">商品ID</param>
            <returns>评分统计</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.CreateReview(Ecommerce.Application.Features.Reviews.Commands.CreateReview.CreateReviewCommand)">
            <summary>
            创建评价 (需要登录和购买过商品)
            </summary>
            <param name="command">创建评价命令</param>
            <returns>创建的评价</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.AddReviewReply(System.Guid,Ecommerce.Application.Features.Reviews.Commands.AddReviewReply.AddReviewReplyCommand)">
            <summary>
            添加评价回复 (需要登录，管理员或商户?)
            </summary>
            <param name="reviewId">评价ID</param>
            <param name="command">添加评价回复命令</param>
            <returns>创建的回复</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.UpdateReviewStatus(System.Guid,Ecommerce.Application.Features.Reviews.Commands.UpdateReviewStatus.UpdateReviewStatusCommand)">
            <summary>
            更新评价状态 (管理员)
            </summary>
            <param name="id">评价ID</param>
            <param name="command">更新评价状态命令 (Published, Rejected, Deleted)</param>
            <returns>更新后的评价</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.DeleteReview(System.Guid)">
            <summary>
            删除评价 (评价作者或管理员)
            </summary>
            <param name="id">评价ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ReviewsApiController.UpdateReview(System.Guid,Ecommerce.Application.Features.Reviews.Commands.UpdateReview.UpdateReviewCommand)">
            <summary>
            更新评价 (评价作者)
            </summary>
            <param name="id">评价ID</param>
            <param name="command">更新评价命令</param>
            <returns>更新后的评价</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController">
            <summary>
            角色继承管理控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.#ctor(Ecommerce.Application.Interfaces.Identity.IRoleInheritanceService,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController},MediatR.ISender)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.GetAllInheritancesAsync">
            <summary>
            获取所有角色继承关系
            </summary>
            <returns>所有角色继承关系列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.AddInheritance(Ecommerce.Application.Features.Identity.Commands.RoleInheritance.AddInheritanceCommand)">
            <summary>
            添加角色继承关系
            </summary>
            <param name="request">添加角色继承关系请求</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.RemoveInheritance(System.String,System.String)">
            <summary>
            移除角色继承关系
            </summary>
            <param name="parentRoleId">父角色ID</param>
            <param name="childRoleId">子角色ID</param>
            <returns>移除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.GetParentRoles(System.String)">
            <summary>
            获取角色的所有父角色
            </summary>
            <param name="roleId">角色ID</param>
            <returns>父角色ID列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.GetChildRoles(System.String)">
            <summary>
            获取角色的所有子角色
            </summary>
            <param name="roleId">角色ID</param>
            <returns>子角色ID列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.GetInheritedPermissions(System.String)">
            <summary>
            获取角色的所有继承权限
            </summary>
            <param name="roleId">角色ID</param>
            <returns>权限列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.ExistsInheritance(System.String,System.String)">
            <summary>
            检查角色继承关系是否存在
            </summary>
            <param name="parentRoleId">父角色ID</param>
            <param name="childRoleId">子角色ID</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RoleInheritanceApiController.WouldCreateCyclicDependency(System.String,System.String)">
            <summary>
            检查添加继承关系是否会导致循环依赖
            </summary>
            <param name="parentRoleId">父角色ID</param>
            <param name="childRoleId">子角色ID</param>
            <returns>是否会导致循环依赖</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.RolesApiController">
            <summary>
            角色管理控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.#ctor(MediatR.IMediator,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.RolesApiController})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.GetAllRoles">
            <summary>
            获取所有角色
            </summary>
            <returns>角色列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.GetRoleById(System.String)">
            <summary>
            根据ID获取角色
            </summary>
            <param name="id">角色ID</param>
            <returns>角色信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.CreateRole(Ecommerce.Application.Features.Identity.Commands.Roles.CreateRoleCommand)">
            <summary>
            创建角色
            </summary>
            <param name="command">角色创建命令</param>
            <returns>创建的角色</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.UpdateRole(System.String,Ecommerce.Application.Features.Identity.Commands.Roles.UpdateRoleCommand)">
            <summary>
            更新角色
            </summary>
            <param name="id">角色ID</param>
            <param name="command">角色更新命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.RolesApiController.DeleteRole(System.String)">
            <summary>
            删除角色
            </summary>
            <param name="id">角色ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.SearchApiController">
            <summary>
            搜索API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SearchApiController.#ctor(Ecommerce.Application.Cache.ISearchHistoryService)">
            <summary>
            Constructor for dependency injection
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SearchApiController.GetHotKeywords(System.Int32)">
            <summary>
            获取热门搜索词
            </summary>
            <param name="count">返回数量</param>
            <returns>热门搜索词列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SearchApiController.GetSearchSuggestions(System.String,System.Int32)">
            <summary>
            获取搜索建议
            </summary>
            <param name="q">搜索前缀</param>
            <param name="count">返回数量</param>
            <returns>搜索建议列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SearchApiController.GetPopularProductSuggestions(System.Int32)">
            <summary>
            获取热门商品建议
            </summary>
            <param name="count">返回数量</param>
            <returns>热门商品建议列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SearchApiController.ClearSearchHistory">
            <summary>
            清除搜索历史
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ShippingApiController">
            <summary>
            物流API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.CalculateRates(Ecommerce.Application.Features.Shipping.Commands.CalculateShippingRates.CalculateShippingRatesCommand)">
            <summary>
            计算物流费率
            </summary>
            <param name="command">命令</param>
            <returns>物流费率列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.CreateShipment(Ecommerce.Application.Features.Shipping.Commands.CreateShipment.CreateShipmentCommand)">
            <summary>
            创建物流单
            </summary>
            <param name="command">命令</param>
            <returns>物流单</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.UpdateStatus(Ecommerce.Application.Features.Shipping.Commands.UpdateShipmentStatus.UpdateShipmentStatusCommand)">
            <summary>
            更新物流单状态
            </summary>
            <param name="command">命令</param>
            <returns>更新后的物流单</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.GetShipment(System.Guid)">
            <summary>
            获取物流单
            </summary>
            <param name="id">物流单ID</param>
            <returns>物流单</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.GetShipmentsByOrder(System.Guid)">
            <summary>
            获取订单物流单
            </summary>
            <param name="orderId">订单ID</param>
            <returns>物流单列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.GetProviders(System.Boolean)">
            <summary>
            获取物流服务提供商
            </summary>
            <param name="activeOnly">是否只获取激活的</param>
            <returns>物流服务提供商列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingApiController.GetOrigins(System.Nullable{System.Guid})">
            <summary>
            获取发货地址
            </summary>
            <param name="merchantId">商户ID (可选)</param>
            <returns>发货地址列表</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController">
            <summary>
            物流区域API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController.GetAreas(System.Nullable{System.Guid},System.Int32,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            获取物流区域列表
            </summary>
            <param name="parentAreaId">父区域ID</param>
            <param name="level">区域级别</param>
            <param name="activeOnly">是否只获取激活的区域</param>
            <param name="includeChildren">是否包含子区域</param>
            <param name="maxChildDepth">子区域最大深度</param>
            <returns>物流区域列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController.GetArea(System.Guid)">
            <summary>
            获取物流区域详情
            </summary>
            <param name="id">区域ID</param>
            <returns>物流区域详情</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController.CreateArea(Ecommerce.Application.Features.Shipping.Commands.CreateShippingArea.CreateShippingAreaCommand)">
            <summary>
            创建物流区域
            </summary>
            <param name="command">创建命令</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController.UpdateArea(System.Guid,Ecommerce.Application.Features.Shipping.Commands.UpdateShippingArea.UpdateShippingAreaCommand)">
            <summary>
            更新物流区域
            </summary>
            <param name="id">区域ID</param>
            <param name="command">更新命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.ShippingAreaApiController.DeleteArea(System.Guid)">
            <summary>
            删除物流区域
            </summary>
            <param name="id">区域ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SocialLoginApiController.GetProviders">
            <summary>
            获取可用的社交登录提供商
            </summary>
            <returns>可用的社交登录提供商列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SocialLoginApiController.Challenge(System.String,System.String)">
            <summary>
            发起社交登录流程
            </summary>
            <param name="provider">社交登录提供商</param>
            <param name="returnUrl">登录成功后的回调URL</param>
            <returns>重定向到提供商登录页面</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SocialLoginApiController.Callback(System.String,System.String)">
            <summary>
            社交登录回调处理
            </summary>
            <param name="provider">社交登录提供商</param>
            <param name="returnUrl">登录成功后的回调URL</param>
            <returns>重定向到客户端应用，附带访问令牌</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SocialLoginApiController.ValidateReturnUrl(System.String,System.String[])">
            <summary>
            验证回调URL是否在允许列表中
            </summary>
            <param name="returnUrl">回调URL</param>
            <param name="allowedHosts">允许的主机列表</param>
            <returns>有效的回调URL或null</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetPublicConfig">
            <summary>
            获取可公开的系统配置信息
            </summary>
            <returns>系统基础配置信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetAdministratorConfig">
            <summary>
            获取完整系统配置（仅管理员可访问）
            </summary>
            <returns>完整系统配置信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetConfigSection(Microsoft.Extensions.Configuration.IConfigurationSection)">
            <summary>
            递归获取配置节
            </summary>
            <param name="section">配置节</param>
            <returns>配置节的JSON对象</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetSystemSettings(System.String)">
            <summary>
            获取系统设置列表
            </summary>
            <param name="group">设置组</param>
            <returns>系统设置列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetSystemSettingByKey(System.String)">
            <summary>
            根据键获取系统设置
            </summary>
            <param name="key">设置键</param>
            <returns>系统设置</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.CreateSystemSetting(Ecommerce.Application.Features.Settings.Commands.CreateSystemSetting.CreateSystemSettingCommand)">
            <summary>
            创建系统设置
            </summary>
            <param name="command">创建系统设置命令</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.UpdateSystemSetting(System.String,Ecommerce.Application.Features.Settings.Commands.UpdateSystemSetting.UpdateSystemSettingCommand)">
            <summary>
            更新系统设置
            </summary>
            <param name="key">设置键</param>
            <param name="command">更新系统设置命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.DeleteSystemSetting(System.String)">
            <summary>
            删除系统设置
            </summary>
            <param name="key">设置键</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.UpdateSystemSettingsBatch(System.Collections.Generic.List{Ecommerce.Application.Features.Settings.Commands.UpdateSystemSetting.UpdateSystemSettingCommand})">
            <summary>
            批量更新系统设置
            </summary>
            <param name="commands">更新系统设置命令列表</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetDefaultMerchantApprovalRequirement">
            <summary>
            获取默认商户商品审核要求
            </summary>
            <returns>默认商户商品审核要求</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.SetDefaultMerchantApprovalRequirement(System.Boolean)">
            <summary>
            设置默认商户商品审核要求
            </summary>
            <param name="requiresApproval">是否需要审核</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetSupportedTranslationLanguages">
            <summary>
            获取支持的翻译语言列表
            </summary>
            <returns>支持的翻译语言列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.UpdateSupportedTranslationLanguages(System.Collections.Generic.List{System.String})">
            <summary>
            更新支持的翻译语言列表
            </summary>
            <param name="languageCodes">语言代码列表</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.GetAITranslationStatus">
            <summary>
            获取AI翻译开关状态
            </summary>
            <returns>AI翻译开关状态</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.SystemConfigApiController.SetAITranslationStatus(System.Boolean)">
            <summary>
            设置AI翻译开关状态
            </summary>
            <param name="isEnabled">是否启用</param>
            <returns>设置结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.TaxApiController">
            <summary>
            税务API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TaxApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.TaxApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TaxApiController.GetTaxRate(System.String,System.String)">
            <summary>
            获取税率
            </summary>
            <param name="countryCode">国家代码</param>
            <param name="regionCode">地区代码</param>
            <returns>税率</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TaxApiController.CalculateTax(Ecommerce.Application.Features.Tax.Commands.CalculateTaxCommand)">
            <summary>
            计算税费
            </summary>
            <param name="command">计算税费命令</param>
            <returns>税费计算结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TaxApiController.AddProductCategoryTaxRate(Ecommerce.Application.Features.Tax.Commands.AddProductCategoryTaxRateCommand)">
            <summary>
            添加商品类别税率关联
            </summary>
            <param name="command">添加商品类别税率关联命令</param>
            <returns>商品类别税率关联</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TaxApiController.GetProductCategoryTaxRates(System.Guid)">
            <summary>
            获取商品类别税率关联
            </summary>
            <param name="productCategoryId">商品类别ID</param>
            <returns>商品类别税率关联列表</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.TicketsApiController">
            <summary>
            工单API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.TicketsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.CreateTicket(Ecommerce.Application.Features.Tickets.Commands.CreateTicket.CreateTicketCommand)">
            <summary>
            创建工单
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.AddTicketReply(System.Guid,Ecommerce.Application.Features.Tickets.Commands.AddTicketReply.AddTicketReplyCommand)">
            <summary>
            添加工单回复
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.UpdateTicketStatus(System.Guid,Ecommerce.Application.Features.Tickets.Commands.UpdateTicketStatus.UpdateTicketStatusCommand)">
            <summary>
            更新工单状态
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetTicketById(System.Guid)">
            <summary>
            根据ID获取工单
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetMyTickets(System.Nullable{Ecommerce.Domain.ValueObjects.TicketStatus},System.Int32,System.Int32,System.Boolean)">
            <summary>
            获取当前用户的工单列表
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetAllTickets(System.Nullable{Ecommerce.Domain.ValueObjects.TicketStatus},System.Nullable{Ecommerce.Domain.ValueObjects.TicketPriority},System.Nullable{System.Guid},System.Int32,System.Int32,System.Boolean)">
            <summary>
            获取所有工单列表（管理员）
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetTicketCategories(System.Boolean)">
            <summary>
            获取工单分类列表
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetBusinessReferences(Ecommerce.Domain.ValueObjects.TicketBusinessType)">
            <summary>
            获取业务单据引用
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.CreateTicketCategory(Ecommerce.Application.Features.Tickets.Commands.CreateTicketCategory.CreateTicketCategoryCommand)">
            <summary>
            创建工单分类（管理员）
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TicketsApiController.GetCurrentUserId">
            <summary>
            获取当前用户ID（Guid类型）
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController">
            <summary>
            翻译管理API控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.#ctor(Ecommerce.Application.Interfaces.External.ITranslationService,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.TranslationsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="translationService">翻译服务</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.GetSupportedLanguages">
            <summary>
            获取支持的语言列表
            </summary>
            <returns>支持的语言列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.GetTranslationQuota">
            <summary>
            获取翻译配额信息
            </summary>
            <returns>翻译配额信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.GetProductTranslations(System.Guid)">
            <summary>
            获取商品翻译
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.UpdateProductTranslations(System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateProductTranslations.UpdateProductTranslationsCommand)">
            <summary>
            更新商品翻译
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.RetranslateProduct(System.Guid,System.String)">
            <summary>
            重新翻译商品
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.GetCategoryTranslations(System.Guid)">
            <summary>
            获取分类翻译
            </summary>
            <param name="id">分类ID</param>
            <returns>分类翻译信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.UpdateCategoryTranslations(System.Guid,Ecommerce.Application.Features.Products.Commands.UpdateCategoryTranslations.UpdateCategoryTranslationsCommand)">
            <summary>
            更新分类翻译
            </summary>
            <param name="id">分类ID</param>
            <param name="command">更新分类翻译命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.RetranslateCategory(System.Guid,System.String)">
            <summary>
            重新翻译分类
            </summary>
            <param name="id">分类ID</param>
            <param name="sourceLanguage">源语言代码</param>
            <returns>翻译结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.GetCouponTranslations(System.Guid)">
            <summary>
            获取优惠券翻译
            </summary>
            <param name="id">优惠券ID</param>
            <returns>优惠券翻译信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.UpdateCouponTranslations(System.Guid,Ecommerce.Application.Features.Marketing.Commands.UpdateCouponTranslations.UpdateCouponTranslationsCommand)">
            <summary>
            更新优惠券翻译
            </summary>
            <param name="id">优惠券ID</param>
            <param name="command">更新优惠券翻译命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.TranslationsApiController.RetranslateCoupon(System.Guid,System.String)">
            <summary>
            重新翻译优惠券
            </summary>
            <param name="id">优惠券ID</param>
            <param name="sourceLanguage">源语言代码</param>
            <returns>翻译结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.GetCurrentUserAddresses">
            <summary>
            获取当前用户的所有地址
            </summary>
            <returns>用户地址列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.AddUserAddress(Ecommerce.Application.Features.Identity.Commands.AddAddress.AddAddressCommand)">
            <summary>
            添加新地址
            </summary>
            <param name="command">地址信息</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.UpdateUserAddress(System.String,Ecommerce.Application.Features.Identity.Commands.UpdateAddress.UpdateAddressCommand)">
            <summary>
            更新地址
            </summary>
            <param name="id">地址ID</param>
            <param name="command">更新的地址信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.DeleteUserAddress(System.String)">
            <summary>
            删除地址
            </summary>
            <param name="id">地址ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.GetAllUserAddressesByAdmin(System.String,System.Int32,System.Int32)">
            <summary>
            管理员获取所有用户地址
            </summary>
            <param name="userId">用户ID（可选）</param>
            <param name="pageNumber">页码</param>
            <param name="pageSize">每页记录数</param>
            <returns>用户地址列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserAddressesApiController.GetUserAddressesByUserId(System.String)">
            <summary>
            管理员获取指定用户的地址
            </summary>
            <param name="userId">用户ID</param>
            <returns>用户地址列表</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController">
            <summary>
            用户优惠券控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController.GetUserCoupons(System.Nullable{System.Int32},System.Int32,System.Int32)">
            <summary>
            获取当前用户的优惠券列表
            </summary>
            <param name="status">优惠券状态</param>
            <param name="skip">跳过数量</param>
            <param name="take">获取数量</param>
            <returns>优惠券列表</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController.ClaimCoupon(Ecommerce.Application.Features.Marketing.Commands.ClaimCouponCommand)">
            <summary>
            领取优惠券
            </summary>
            <param name="command">领取优惠券命令</param>
            <returns>用户优惠券ID</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserCouponsApiController.UseCoupon(System.Guid)">
            <summary>
            使用优惠券
            </summary>
            <param name="couponId">优惠券ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController">
            <summary>
            用户角色管理控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.UserRolesApiController})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.GetUserRoles(System.String)">
            <summary>
            获取用户的角色
            </summary>
            <param name="userId">用户ID</param>
            <returns>用户角色信息</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.AssignRoleToUser(System.String,System.String)">
            <summary>
            为用户分配角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="roleId">角色ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.RemoveRoleFromUser(System.String,System.String)">
            <summary>
            从用户移除角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="roleId">角色ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.UpdateUserRoles(System.String,Ecommerce.Application.Features.Identity.Commands.UserRoles.UpdateUserRolesCommand)">
            <summary>
            更新用户角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="command">更新用户角色命令</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UserRolesApiController.GetUserRoles">
            <summary>
            获取当前用户所有角色
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.GetCurrentUser">
            <summary>
            获取当前用户信息
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.GetUser(System.String)">
            <summary>
            获取指定用户信息
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.UpdateCurrentUser(Ecommerce.Application.Features.Identity.Commands.UpdateUser.UpdateUserCommand)">
            <summary>
            更新当前用户信息
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.UpdateUser(System.String,Ecommerce.Application.Features.Identity.Commands.UpdateUser.UpdateUserCommand)">
            <summary>
            更新指定用户信息
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.GetUsers(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.Int32,System.Int32)">
            <summary>
            获取用户列表
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.CreateUser(Ecommerce.Application.Features.Identity.Commands.CreateUser.CreateUserCommand)">
            <summary>
            创建用户
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.DeleteUser(System.String)">
            <summary>
            删除用户
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.UpdateUserStatus(System.String,Ecommerce.Application.Features.Identity.Commands.UpdateUserStatus.UpdateUserStatusCommand)">
            <summary>
            更新用户状态
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.SuspendUser(System.String,Ecommerce.Application.Features.Identity.Commands.SuspendUser.SuspendUserCommand)">
            <summary>
            暂停用户
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.UsersApiController.GetSystemUsers(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.String[],System.Int32,System.Int32)">
            <summary>
            获取系统用户列表
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController">
            <summary>
            Shippo Webhook 控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.HandleWebhook">
            <summary>
            处理 Shippo Webhook
            </summary>
            <returns>处理结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.HandleTrackUpdatedEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload)">
            <summary>
            处理跟踪更新事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.HandleTransactionCreatedEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload)">
            <summary>
            处理交易创建事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.HandleTransactionUpdatedEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload)">
            <summary>
            处理交易更新事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookApiController.MapTrackingStatusToShipmentStatus(System.String)">
            <summary>
            将跟踪状态映射为物流单状态
            </summary>
            <param name="trackingStatus">跟踪状态</param>
            <returns>物流单状态</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload">
            <summary>
            Shippo Webhook 负载
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload.Event">
            <summary>
            事件类型
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookPayload.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData">
            <summary>
            Shippo Webhook 数据
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.ObjectId">
            <summary>
            对象ID
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.ObjectStatus">
            <summary>
            对象状态
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.TrackingNumber">
            <summary>
            跟踪号
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.TrackingStatus">
            <summary>
            跟踪状态
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.Carrier">
            <summary>
            承运人
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShippoWebhookData.ServiceLevel">
            <summary>
            服务级别
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController">
            <summary>
            ShipStation Webhook 控制器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleWebhook">
            <summary>
            处理 ShipStation Webhook
            </summary>
            <returns>处理结果</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleOrderNotifyEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload)">
            <summary>
            处理订单通知事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleShipNotifyEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload)">
            <summary>
            处理发货通知事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleItemShipNotifyEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload)">
            <summary>
            处理物品发货通知事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleShippedEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload)">
            <summary>
            处理已发货事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookApiController.HandleDeliveredEvent(Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload)">
            <summary>
            处理已送达事件
            </summary>
            <param name="payload">Webhook 负载</param>
            <returns>任务</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload">
            <summary>
            ShipStation Webhook 负载
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.ResourceType">
            <summary>
            资源类型
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.EventType">
            <summary>
            事件类型
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.ResourceUrl">
            <summary>
            资源URL
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.ResourceId">
            <summary>
            资源ID
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.StoreId">
            <summary>
            店铺ID
            </summary>
        </member>
        <member name="P:Ecommerce.Web.Api.Controllers.V1.Webhooks.ShipStationWebhookPayload.SendDate">
            <summary>
            发送时间
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Filters.ApiResponseFilter">
            <summary>
            统一API响应格式过滤器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ApiResponseFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            在Action执行前的操作
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ApiResponseFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)">
            <summary>
            在Action执行后的操作，统一API响应格式
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ApiResponseFilter.IsApiResponseType(System.Object)">
            <summary>
            检查是否已经是ApiResponse类型
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ApiResponseFilter.GetDefaultMessageForStatusCode(System.Int32)">
            <summary>
            根据状态码获取默认消息
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Filters.ShippingAddressDtoSchemaFilter">
            <summary>
            为物流地址DTO提供自定义Schema ID的过滤器
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ShippingAddressDtoSchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext)">
            <summary>
            应用过滤器
            </summary>
            <param name="schema">OpenAPI Schema</param>
            <param name="context">Schema过滤器上下文</param>
        </member>
        <member name="T:Ecommerce.Web.Api.Filters.ValidateAntiForgeryTokenFilter">
            <summary>
            CSRF防护过滤器，用于验证防伪令牌
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.ValidateAntiForgeryTokenFilter.OnAuthorizationAsync(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            授权验证方法
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Filters.AntiForgeryExtensions">
            <summary>
            CSRF防护扩展
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Filters.AntiForgeryExtensions.UseAntiForgeryToken(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Antiforgery.IAntiforgery)">
            <summary>
            生成防伪令牌中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Helpers.LocalizationHelper">
            <summary>
            本地化辅助类
            </summary>
        </member>
        <member name="F:Ecommerce.Web.Api.Helpers.LocalizationHelper.DefaultLocale">
            <summary>
            默认语言代码
            </summary>
        </member>
        <member name="F:Ecommerce.Web.Api.Helpers.LocalizationHelper.SupportedLocales">
            <summary>
            支持的语言列表
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Helpers.LocalizationHelper.GetCurrentLocale(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            获取当前请求的语言标识符
            </summary>
            <param name="httpContext">HTTP上下文</param>
            <returns>语言标识符</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Helpers.LocalizationHelper.IsValidLocale(System.String)">
            <summary>
            检查语言代码是否有效
            </summary>
            <param name="locale">语言代码</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:Ecommerce.Web.Api.Helpers.LocalizationHelper.GetLocaleDisplayName(System.String)">
            <summary>
            获取语言的显示名称
            </summary>
            <param name="locale">语言代码</param>
            <returns>语言显示名称</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Hubs.NotificationHub">
            <summary>
            通知消息Hub
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Hubs.NotificationHub.#ctor(Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Hubs.NotificationHub},Ecommerce.Application.Interfaces.Identity.ICurrentUserService)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Hubs.NotificationHub.OnConnectedAsync">
            <summary>
            连接建立时
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Hubs.NotificationHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            连接断开时
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Hubs.NotificationHub.JoinGroup(System.String)">
            <summary>
            加入用户组
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Hubs.NotificationHub.LeaveGroup(System.String)">
            <summary>
            离开用户组
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.AntiForgeryMiddleware">
            <summary>
            防伪令牌中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware">
            <summary>
            自定义权限验证中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.ShouldSkipAuthorization(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            判断是否应跳过授权检查
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.IsAdminEndpoint(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            判断是否为管理员API端点
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.UserHasRole(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            检查用户是否有指定角色
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.DetermineRequiredPermission(Microsoft.AspNetCore.Http.PathString)">
            <summary>
            根据路径确定所需权限
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.UserHasPermissionAsync(System.Guid,System.String)">
            <summary>
            检查用户是否有指定权限
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.RequiresOwnershipCheck(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            判断是否需要所有权检查
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.AuthorizationMiddleware.IsResourceOwner(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            检查当前用户是否为资源所有者
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.GlobalExceptionHandlerMiddleware">
            <summary>
            全局异常处理中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.LocalizationMiddleware">
            <summary>
            本地化中间件，处理请求中的语言标识符
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.LocalizationMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Logging.ILogger{Ecommerce.Web.Api.Middlewares.LocalizationMiddleware})">
            <summary>
            构造函数
            </summary>
            <param name="next">请求委托</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.LocalizationMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            处理请求
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>任务</returns>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions">
            <summary>
            中间件扩展方法
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseCustomExceptionHandler(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加全局异常处理中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseCustomRequestSanitization(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加请求净化中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseCustomAntiForgeryToken(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Antiforgery.IAntiforgery)">
            <summary>
            添加防伪令牌中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseCustomAuthorization(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加自定义授权中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseCustomLocalization(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加本地化中间件
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.MiddlewareExtensions.UseSwaggerAntiforgeryToken(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Antiforgery.IAntiforgery)">
            <summary>
            添加Swagger防伪令牌中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.RequestSanitizationMiddleware">
            <summary>
            请求净化中间件，用于防止XSS攻击
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.RequestSanitizationMiddlewareExtensions">
            <summary>
            请求净化中间件扩展
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Middlewares.RequestSanitizationMiddlewareExtensions.UseRequestSanitization(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用请求净化中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Middlewares.SwaggerAntiforgeryMiddleware">
            <summary>
            Swagger防伪令牌中间件
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Services.NotificationHubService">
            <summary>
            通知Hub服务实现
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Services.NotificationHubService.#ctor(Microsoft.AspNetCore.SignalR.IHubContext{Ecommerce.Web.Api.Hubs.NotificationHub})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Services.NotificationHubService.SendToUserAsync(System.String,System.Object,System.String)">
            <summary>
            向指定用户发送通知
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Services.NotificationHubService.SendToGroupAsync(System.String,System.Object,System.String)">
            <summary>
            向指定组发送通知
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Services.NotificationHubService.SendToAllAsync(System.Object,System.String)">
            <summary>
            向所有连接的客户端发送通知
            </summary>
        </member>
        <member name="T:Ecommerce.Web.Api.Services.NotificationServiceRegistration">
            <summary>
            通知服务注册扩展
            </summary>
        </member>
        <member name="M:Ecommerce.Web.Api.Services.NotificationServiceRegistration.AddNotificationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加通知相关服务
            </summary>
        </member>
    </members>
</doc>
