using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Ecommerce.Domain.Aggregates.Identity;

namespace Ecommerce.Application.Extensions
{
    /// <summary>
    /// 用户扩展方法
    /// </summary>
    public static class UserExtensions
    {
        /// <summary>
        /// 判断用户是否为系统用户（非Customer角色的用户）
        /// </summary>
        /// <param name="userManager">用户管理器</param>
        /// <param name="user">用户</param>
        /// <returns>是否为系统用户</returns>
        public static async Task<bool> IsSystemUserAsync(this UserManager<ApplicationUser> userManager, ApplicationUser user)
        {
            if (user == null)
                return false;

            var roles = await userManager.GetRolesAsync(user);
            
            // 如果用户没有任何角色，则不是系统用户
            if (!roles.Any())
                return false;

            // 如果用户只有Customer角色，则不是系统用户
            if (roles.Count == 1 && roles.Contains("Customer"))
                return false;

            // 如果用户有除Customer之外的其他角色，则是系统用户
            return roles.Any(role => role != "Customer");
        }

        /// <summary>
        /// 更新用户的系统用户标识
        /// </summary>
        /// <param name="userManager">用户管理器</param>
        /// <param name="user">用户</param>
        /// <returns>更新结果</returns>
        public static async Task<IdentityResult> UpdateSystemUserFlagAsync(this UserManager<ApplicationUser> userManager, ApplicationUser user)
        {
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "用户不能为空" });

            var isSystemUser = await userManager.IsSystemUserAsync(user);
            user.SetSystemUser(isSystemUser);
            
            return await userManager.UpdateAsync(user);
        }

        /// <summary>
        /// 批量更新用户的系统用户标识
        /// </summary>
        /// <param name="userManager">用户管理器</param>
        /// <param name="users">用户列表</param>
        /// <returns>更新结果</returns>
        public static async Task<List<(ApplicationUser User, bool Success, string? Error)>> UpdateSystemUserFlagsAsync(
            this UserManager<ApplicationUser> userManager, 
            IEnumerable<ApplicationUser> users)
        {
            var results = new List<(ApplicationUser User, bool Success, string? Error)>();

            foreach (var user in users)
            {
                try
                {
                    var result = await userManager.UpdateSystemUserFlagAsync(user);
                    if (result.Succeeded)
                    {
                        results.Add((user, true, null));
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        results.Add((user, false, errors));
                    }
                }
                catch (System.Exception ex)
                {
                    results.Add((user, false, ex.Message));
                }
            }

            return results;
        }
    }
}
