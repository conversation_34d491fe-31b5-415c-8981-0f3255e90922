using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.SuspendUser
{
    /// <summary>
    /// 暂停用户命令处理器
    /// </summary>
    public class SuspendUserCommandHandler : IRequestHandler<SuspendUserCommand, SuspendUserResponse>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICacheService _cacheService;
        private readonly ILogger<SuspendUserCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SuspendUserCommandHandler(
            UserManager<ApplicationUser> userManager,
            ICacheService cacheService,
            ILogger<SuspendUserCommandHandler> logger)
        {
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 处理暂停用户命令
        /// </summary>
        public async Task<SuspendUserResponse> Handle(SuspendUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理暂停用户命令: {UserId}", request.UserId);

                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId);
                if (user == null)
                {
                    _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                    return new SuspendUserResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_NOT_FOUND",
                        Errors = new[] { "用户不存在" }
                    };
                }

                // 检查用户当前状态
                if (user.Status == UserStatus.Deleted)
                {
                    _logger.LogWarning("无法暂停已删除的用户: {UserId}", request.UserId);
                    return new SuspendUserResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_DELETED",
                        Errors = new[] { "无法暂停已删除的用户" }
                    };
                }

                if (user.Status == UserStatus.Suspended)
                {
                    _logger.LogWarning("用户已被暂停: {UserId}", request.UserId);
                    return new SuspendUserResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_ALREADY_SUSPENDED",
                        Errors = new[] { "用户已被暂停" }
                    };
                }

                // 暂停用户
                user.Suspend();

                // 如果设置了暂停到期时间，使用Identity的锁定功能
                if (request.SuspendUntil.HasValue)
                {
                    user.LockoutEnabled = true;
                    user.LockoutEnd = new DateTimeOffset(request.SuspendUntil.Value);
                }

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                // 更新用户
                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = result.Errors.Select(e => e.Description).ToArray();
                    _logger.LogError("暂停用户失败: {UserId}, 错误: {Errors}", request.UserId, string.Join(", ", errors));
                    return new SuspendUserResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "SUSPEND_FAILED",
                        Errors = errors
                    };
                }

                // 清除缓存
                try
                {
                    await _cacheService.RemoveByPatternAsync($"*:Users:{request.UserId}*");
                    await _cacheService.RemoveByPatternAsync($"*:Users:List*");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清除用户缓存时发生错误: {UserId}", request.UserId);
                }

                _logger.LogInformation("成功暂停用户: {UserId}, 原因: {Reason}, 到期时间: {SuspendUntil}",
                    request.UserId, request.Reason ?? "未提供", request.SuspendUntil?.ToString() ?? "无限期");

                return new SuspendUserResponse
                {
                    IsSuccessful = true,
                    UserId = user.Id.ToString(),
                    UserName = user.UserName,
                    Status = "Suspended"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停用户时发生错误: {UserId}, 错误: {Message}", request.UserId, ex.Message);
                return new SuspendUserResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "UNEXPECTED_ERROR",
                    Errors = new[] { ex.Message }
                };
            }
        }
    }
}
