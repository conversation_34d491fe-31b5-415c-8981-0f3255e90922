using MediatR;

namespace Ecommerce.Application.Features.Identity.Commands.DeleteUser;

/// <summary>
/// 删除用户命令（软删除/销户）
/// </summary>
public record DeleteUserCommand : IRequest<DeleteUserResponse>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; init; } = default!;

    /// <summary>
    /// 删除原因
    /// </summary>
    public string? Reason { get; init; }

    /// <summary>
    /// 是否强制删除（即使用户有未完成的订单等）
    /// </summary>
    public bool ForceDelete { get; init; } = false;

    /// <summary>
    /// 是否硬删除（物理删除，默认为软删除）
    /// </summary>
    public bool HardDelete { get; init; } = false;
}
