using Ecommerce.Application.Cache;
using Ecommerce.Application.Extensions;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Queries.GetUser
{
    /// <summary>
    /// 获取用户信息查询处理器
    /// </summary>
    public class GetUserQueryHandler(
        UserManager<ApplicationUser> userManager,
        ICacheService cacheService,
        CachePolicyFactory cachePolicyFactory,
        ILogger<GetUserQueryHandler> logger) : IRequestHandler<GetUserQuery, GetUserResponse?>
    {
        private readonly UserManager<ApplicationUser> _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        private readonly ICacheService _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        private readonly CachePolicyFactory _cachePolicyFactory = cachePolicyFactory ?? throw new ArgumentNullException(nameof(cachePolicyFactory));
        private readonly ILogger<GetUserQueryHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// 处理获取用户信息查询
        /// </summary>
        /// <param name="request">查询</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>用户信息</returns>
        public async Task<GetUserResponse?> Handle(GetUserQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始获取用户信息: {UserId}", request.UserId);

                // 创建缓存策略
                var cachePolicy = _cachePolicyFactory.CreateUserProfilePolicy(request.UserId);

                // 使用缓存获取用户信息
                return await _cacheService.GetOrAddAsync(
                    cachePolicy,
                    async () =>
                    {
                        // 获取用户
                        var user = await _userManager.FindByIdAsync(request.UserId);
                        if (user == null)
                        {
                            _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                            return null;
                        }

                        // 检查是否为系统用户
                        var isSystemUser = await _userManager.IsSystemUserAsync(user);

                        // 返回用户信息
                        return new GetUserResponse
                        {
                            Id = user.Id.ToString(),
                            UserName = user.UserName ?? string.Empty,
                            Email = user.Email ?? string.Empty,
                            PhoneNumber = user.PhoneNumber,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            AvatarUrl = user.AvatarUrl,
                            CreatedAt = user.CreatedAt,
                            LastLoginAt = user.LastLoginAt,
                            IsActive = user.Status == Domain.Enums.UserStatus.Active,
                            IsEmailConfirmed = user.EmailConfirmed,
                            IsPhoneConfirmed = user.PhoneNumberConfirmed,
                            Status = user.Status.ToString(),
                            IsSystemUser = isSystemUser,
                            DeletedAt = user.DeletedAt
                        };
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误: {UserId}", request.UserId);
                throw;
            }
        }
    }
}
