using System.Collections.Generic;

namespace Ecommerce.Application.Features.Identity.Commands.CancelDeactivation
{
    /// <summary>
    /// 取消账户注销响应
    /// </summary>
    public class CancelDeactivationResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public IEnumerable<string>? Errors { get; set; }
    }
}
