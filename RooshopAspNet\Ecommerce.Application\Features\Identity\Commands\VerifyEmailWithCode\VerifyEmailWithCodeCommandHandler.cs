using Ecommerce.Application.Interfaces.External;
using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Ecommerce.Application.Features.Identity.Commands.VerifyEmailWithCode;

/// <summary>
/// 使用验证码验证邮箱命令处理程序
/// </summary>
public class VerifyEmailWithCodeCommandHandler : IRequestHandler<VerifyEmailWithCodeCommand, VerifyEmailWithCodeResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenService _tokenService;
    private readonly IVerificationCodeService _verificationCodeService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="userManager">用户管理器</param>
    /// <param name="signInManager">登录管理器</param>
    /// <param name="tokenService">令牌服务</param>
    /// <param name="verificationCodeService">验证码服务</param>
    public VerifyEmailWithCodeCommandHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenService tokenService,
        IVerificationCodeService verificationCodeService)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenService = tokenService;
        _verificationCodeService = verificationCodeService;
    }

    /// <summary>
    /// 处理使用验证码验证邮箱命令
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<VerifyEmailWithCodeResponse> Handle(VerifyEmailWithCodeCommand request, CancellationToken cancellationToken)
    {
        // 查找用户
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user == null)
        {
            return new VerifyEmailWithCodeResponse
            {
                IsSuccessful = false,
                Errors = ["找不到指定的用户"]
            };
        }

        // 检查邮箱是否已确认
        if (await _userManager.IsEmailConfirmedAsync(user))
        {
            return new VerifyEmailWithCodeResponse
            {
                IsSuccessful = false,
                Errors = ["邮箱已经确认，无需验证"]
            };
        }

        // 验证验证码
        bool isCodeValid = await _verificationCodeService.ValidateCodeAsync(
            user.Id.ToString(),
            "EmailConfirmation",
            request.Code);

        if (!isCodeValid)
        {
            return new VerifyEmailWithCodeResponse
            {
                IsSuccessful = false,
                Errors = ["验证码无效或已过期"]
            };
        }

        // 确认邮箱
        user.EmailConfirmed = true;
        var updateResult = await _userManager.UpdateAsync(user);

        if (!updateResult.Succeeded)
        {
            return new VerifyEmailWithCodeResponse
            {
                IsSuccessful = false,
                Errors = updateResult.Errors.Select(e => e.Description)
            };
        }

        // 自动登录用户
        await _signInManager.SignInAsync(user, isPersistent: false);

        // 更新最后登录时间
        user.SetLastLoginTime();
        await _userManager.UpdateAsync(user);

        // 生成访问令牌
        var roles = await _userManager.GetRolesAsync(user);
        var accessToken = await _tokenService.CreateAccessTokenAsync(user.Id, user.UserName!, user.Email!, roles);
        var refreshToken = await _tokenService.CreateRefreshTokenAsync(user);
        var expiresIn = _tokenService.GetAccessTokenExpirationSeconds();

        return new VerifyEmailWithCodeResponse
        {
            IsSuccessful = true,
            UserId = user.Id.ToString(),
            UserName = user.UserName!,
            Email = user.Email!,
            Token = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = expiresIn,
            Roles = roles.ToArray()
        };
    }
}
