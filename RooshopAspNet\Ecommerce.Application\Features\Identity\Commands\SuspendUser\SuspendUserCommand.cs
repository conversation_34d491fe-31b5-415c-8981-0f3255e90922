using MediatR;

namespace Ecommerce.Application.Features.Identity.Commands.SuspendUser
{
    /// <summary>
    /// 暂停用户命令
    /// </summary>
    public record SuspendUserCommand : IRequest<SuspendUserResponse>
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; init; } = default!;

        /// <summary>
        /// 暂停原因
        /// </summary>
        public string? Reason { get; init; }

        /// <summary>
        /// 暂停到期时间（可选，如果不设置则无限期暂停）
        /// </summary>
        public DateTime? SuspendUntil { get; init; }
    }
}
