"use client"

import { Separator } from "@app/components/ui/separator"
import { SidebarTrigger } from "@app/components/ui/sidebar"
import { useAdminTranslations } from "@rooshop/core/i18n"
import DynamicBreadcrumb from "@app/components/ui/dynamic-breadcrumb"
import { ThemeToggle } from "@app/components/theme-toggle"
import { Button } from "@app/components/ui/button"
import { Search } from "lucide-react"
import { openCommandMenu } from "@app/components/command-menu"
import { LanguageSwitcher } from "@app/components/LanguageSwitcher"
import { usePathname } from "next/navigation"

export function SiteHeader() {
  const pathname = usePathname()
  const tDashboard = useAdminTranslations('dashboard')
  const tUser = useAdminTranslations('user')
  const tMenu = useAdminTranslations('menu')
  const tCommon = useAdminTranslations('common')

  // 根据路径获取页面标题
  const getPageTitle = () => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const relevantSegments = pathSegments.slice(1) // 移除语言前缀

    if (relevantSegments.length === 0 || relevantSegments[0] === 'dashboard') {
      return tDashboard('title')
    }

    // 用户管理相关页面
    if (relevantSegments[0] === 'user') {
      if (relevantSegments[1] === 'list') {
        return tMenu('user_list')
      }
      if (relevantSegments[1] === 'role') {
        return tMenu('role_list')
      }
      return tMenu('user_permission_management')
    }

    // 其他页面可以在这里添加
    // 默认返回仪表盘标题
    return tDashboard('title')
  }

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">{getPageTitle()}</h1>
        <Separator orientation="vertical" className="mr-2 h-4" />
        <DynamicBreadcrumb />
        <div className="ml-auto flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1"
            onClick={openCommandMenu}
          >
            <Search className="h-3.5 w-3.5" />
            <span className="hidden md:inline-flex">{tCommon('search')}...</span>
            <kbd className="pointer-events-none hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 md:inline-flex">
              <span className="text-xs">⌘</span>K
            </kbd>
          </Button>
          <LanguageSwitcher />
          <ThemeToggle />
          {/* 其他右侧按钮可以添加在这里 */}
        </div>
      </div>
    </header>
  )
}
