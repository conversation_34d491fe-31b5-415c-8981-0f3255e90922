using Ecommerce.Application.Common.Models;
using Ecommerce.Application.Features.Identity.Commands.CancelDeactivation;
using Ecommerce.Application.Features.Identity.Commands.ChangePassword;
using Ecommerce.Application.Features.Identity.Commands.ConfirmEmail;
using Ecommerce.Application.Features.Identity.Commands.DeactivateAccount;
using Ecommerce.Application.Features.Identity.Commands.ForgotPassword;
using Ecommerce.Application.Features.Identity.Commands.Login;
using Ecommerce.Application.Features.Identity.Commands.RefreshToken;
using Ecommerce.Application.Features.Identity.Commands.RegisterUser;
using Ecommerce.Application.Features.Identity.Commands.ResendEmailConfirmation;
using Ecommerce.Application.Features.Identity.Commands.ResetPassword;
using Ecommerce.Application.Features.Identity.Commands.ResetPasswordWithCode;
using Ecommerce.Application.Features.Identity.Commands.RevokeToken;
using Ecommerce.Application.Features.Identity.Commands.VerifyEmailWithCode;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Ecommerce.Web.Api.Controllers.V1
{

    /// <summary>
    /// 账户API控制器 - 提供用户认证、注册、密码管理等功能
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Produces("application/json")]
    public class AccountsApiController : BaseApiController
    {
        // 常用错误消息
        private static readonly string[] EmptyEmailError = ["邮箱地址不能为空"];
        private static readonly string[] EmptyPasswordError = ["密码不能为空"];
        private static readonly string[] EmptyUsernameError = ["用户名不能为空"];
        private static readonly string[] EmptyTokenError = ["令牌不能为空"];
        private static readonly string[] EmptyCodeError = ["验证码不能为空"];
        private static readonly string[] InvalidTokenError = ["无效的令牌"];
        private static readonly string[] InvalidUserIdError = ["无效的用户ID"];
        private static readonly string[] InvalidRefreshTokenError = ["无效的刷新令牌"];
        /// <summary>
        /// 用户注册 - 创建新用户账户
        /// </summary>
        /// <param name="command">注册信息</param>
        /// <returns>注册结果</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<RegisterUserResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<RegisterUserResponse>>> Register([FromBody] RegisterUserCommand command)
        {
            try
            {
                // 验证请求
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<RegisterUserResponse>("注册失败", EmptyEmailError, 400);
                }

                if (string.IsNullOrEmpty(command.Password))
                {
                    return ApiFail<RegisterUserResponse>("注册失败", EmptyPasswordError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    // 对于注册失败，返回400状态码和具体错误信息
                    return ApiFail<RegisterUserResponse>("注册失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "注册成功，请检查您的邮箱完成验证");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理用户注册请求时发生错误: {Email}", command.Email);

                // 对于注册异常，返回400状态码而不是500，并提供更具体的错误信息
                return ApiFail<RegisterUserResponse>("注册失败", ["注册过程中发生错误，请检查您的注册信息是否正确"], 400);
            }
        }

        /// <summary>
        /// 用户登录 - 获取访问令牌
        /// </summary>
        /// <param name="command">登录信息</param>
        /// <returns>登录结果（含令牌）</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<LoginResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<LoginResponse>>> Login([FromBody] LoginCommand command)
        {
            try
            {
                // 验证请求
                if (string.IsNullOrEmpty(command.UsernameOrEmail))
                {
                    return ApiFail<LoginResponse>("登录失败", EmptyUsernameError, 400);
                }

                if (string.IsNullOrEmpty(command.Password))
                {
                    return ApiFail<LoginResponse>("登录失败", EmptyPasswordError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<LoginResponse>("登录失败", result.Errors?.ToArray() ?? ["账户或密码错误"], 400);
                }

                return ApiSuccess(result, "登录成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理用户登录请求时发生错误: {UsernameOrEmail}", command.UsernameOrEmail);
                return HandleException(ex, "处理用户登录请求");
            }
        }

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="command">刷新令牌信息</param>
        /// <returns>新的访问令牌和刷新令牌</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<RefreshTokenResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<RefreshTokenResponse>>> RefreshToken([FromBody] RefreshTokenCommand command)
        {
            try
            {
                if (string.IsNullOrEmpty(command.RefreshToken))
                {
                    return ApiFail<RefreshTokenResponse>("刷新令牌失败", EmptyTokenError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<RefreshTokenResponse>("刷新令牌失败", result.Errors ?? InvalidRefreshTokenError, 401);
                }

                return ApiSuccess(result, "令牌刷新成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理刷新令牌请求时发生错误");
                return HandleException(ex, "处理刷新令牌请求");
            }
        }

        /// <summary>
        /// 登出（销毁刷新令牌）
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("logout")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<object>>> Logout([FromBody] LogoutRequest? request = null)
        {
            try
            {
                // 首先尝试从JWT令牌中获取userId
                var userId = User.FindFirst("sub")?.Value;

                // 如果令牌中没有有效的userId，则尝试从请求体中获取
                if (string.IsNullOrEmpty(userId) && request?.UserId != null)
                {
                    userId = request.UserId;
                }

                if (string.IsNullOrEmpty(userId))
                {
                    return ApiFail<object>("登出失败", InvalidUserIdError, 400);
                }

                var command = new RevokeTokenCommand { UserId = userId };
                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<object>("登出失败", result.Errors ?? ["无法销毁令牌"], 400);
                }

                return ApiSuccess<object>(new {}, "登出成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理登出请求时发生错误");
                return HandleException(ex, "处理登出请求");
            }
        }

        /// <summary>
        /// 确认邮箱
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="token">确认令牌</param>
        /// <param name="expires">过期时间</param>
        /// <returns>确认结果</returns>
        [HttpGet("confirm-email")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<ConfirmEmailResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<ConfirmEmailResponse>>> ConfirmEmail([FromQuery] Guid userId, [FromQuery] string token, [FromQuery] string? expires = null)
        {
            try
            {
                if (userId == Guid.Empty)
                {
                    return ApiFail<ConfirmEmailResponse>("邮箱确认失败", InvalidUserIdError, 400);
                }

                if (string.IsNullOrEmpty(token))
                {
                    return ApiFail<ConfirmEmailResponse>("邮箱确认失败", EmptyTokenError, 400);
                }

                var command = new ConfirmEmailCommand
                {
                    UserId = userId,
                    Token = token,
                    Expires = expires
                };

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ConfirmEmailResponse>("邮箱确认失败", result.Errors?.ToArray() ?? InvalidTokenError, 400);
                }

                return ApiSuccess(result, "邮箱确认成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理邮箱确认请求时发生错误: UserId={UserId}, Token={Token}", userId, token);
                return HandleException(ex, "处理邮箱确认请求");
            }
        }

        /// <summary>
        /// 重新发送邮箱确认邮件
        /// </summary>
        /// <param name="command">重新发送邮箱确认邮件命令</param>
        /// <returns>发送结果</returns>
        [HttpPost("resend-email-confirmation")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<ResendEmailConfirmationResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<ResendEmailConfirmationResponse>>> ResendEmailConfirmation([FromBody] ResendEmailConfirmationCommand command)
        {
            try
            {
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<ResendEmailConfirmationResponse>("重新发送邮箱确认邮件失败", EmptyEmailError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ResendEmailConfirmationResponse>("重新发送邮箱确认邮件失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "确认邮件已发送，请检查您的邮箱");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理重新发送邮箱确认邮件请求时发生错误: {Email}", command.Email);
                return HandleException(ex, "处理重新发送邮箱确认邮件请求");
            }
        }

        /// <summary>
        /// 使用验证码验证邮箱
        /// </summary>
        /// <param name="command">验证码验证命令</param>
        /// <returns>验证结果</returns>
        [HttpPost("verify-email-with-code")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<VerifyEmailWithCodeResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<VerifyEmailWithCodeResponse>>> VerifyEmailWithCode([FromBody] VerifyEmailWithCodeCommand command)
        {
            try
            {
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<VerifyEmailWithCodeResponse>("验证邮箱失败", EmptyEmailError, 400);
                }

                if (string.IsNullOrEmpty(command.Code))
                {
                    return ApiFail<VerifyEmailWithCodeResponse>("验证邮箱失败", EmptyCodeError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<VerifyEmailWithCodeResponse>("验证邮箱失败", result.Errors?.ToArray() ?? ["验证码无效或已过期"], 400);
                }

                return ApiSuccess(result, "邮箱验证成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理验证码验证邮箱请求时发生错误: {Email}", command.Email);
                return HandleException(ex, "处理验证码验证邮箱请求");
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="command">修改密码命令</param>
        /// <returns>修改结果</returns>
        [HttpPost("change-password")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<ChangePasswordResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<ChangePasswordResponse>>> ChangePassword([FromBody] ChangePasswordCommand command)
        {
            try
            {
                // 从当前用户中获取用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiFail<ChangePasswordResponse>("修改密码失败", InvalidUserIdError, 401);
                }
                command.UserId = userId;

                if (string.IsNullOrEmpty(command.CurrentPassword))
                {
                    return ApiFail<ChangePasswordResponse>("修改密码失败", EmptyPasswordError, 400);
                }

                if (string.IsNullOrEmpty(command.NewPassword))
                {
                    return ApiFail<ChangePasswordResponse>("修改密码失败", ["新密码不能为空"], 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ChangePasswordResponse>("修改密码失败", result.Errors?.ToArray() ?? ["当前密码错误或新密码不符合要求"], 400);
                }

                return ApiSuccess(result, "密码修改成功");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理修改密码请求时发生错误");
                return HandleException(ex, "处理修改密码请求");
            }
        }

        /// <summary>
        /// 忘记密码 - 发送密码重置邮件
        /// </summary>
        /// <param name="command">忘记密码命令</param>
        /// <returns>发送结果</returns>
        [HttpPost("forgot-password")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<ForgotPasswordResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<ForgotPasswordResponse>>> ForgotPassword([FromBody] ForgotPasswordCommand command)
        {
            try
            {
                // 验证请求
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<ForgotPasswordResponse>("忘记密码失败", EmptyEmailError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ForgotPasswordResponse>("忘记密码失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "密码重置邮件已发送，请检查您的邮箱");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理忘记密码请求时发生错误: {Email}", command.Email);
                return HandleException(ex, "处理忘记密码请求");
            }
        }

        /// <summary>
        /// 重置密码 - 使用重置令牌
        /// </summary>
        /// <param name="command">重置密码命令</param>
        /// <returns>重置结果</returns>
        [HttpPost("reset-password")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<ResetPasswordResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<ResetPasswordResponse>>> ResetPassword([FromBody] ResetPasswordCommand command)
        {
            try
            {
                // 验证请求
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<ResetPasswordResponse>("重置密码失败", EmptyEmailError, 400);
                }

                if (string.IsNullOrEmpty(command.Token))
                {
                    return ApiFail<ResetPasswordResponse>("重置密码失败", EmptyTokenError, 400);
                }

                if (string.IsNullOrEmpty(command.NewPassword))
                {
                    return ApiFail<ResetPasswordResponse>("重置密码失败", EmptyPasswordError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ResetPasswordResponse>("重置密码失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "密码重置成功，请使用新密码登录");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理重置密码请求时发生错误: {Email}", command.Email);
                return HandleException(ex, "处理重置密码请求");
            }
        }

        /// <summary>
        /// 使用验证码重置密码 - 更简单的重置方式
        /// </summary>
        /// <param name="command">使用验证码重置密码命令</param>
        /// <returns>重置结果</returns>
        [HttpPost("reset-password-with-code")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<ResetPasswordWithCodeResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        public async Task<ActionResult<ApiResponse<ResetPasswordWithCodeResponse>>> ResetPasswordWithCode([FromBody] ResetPasswordWithCodeCommand command)
        {
            try
            {
                // 验证请求
                if (string.IsNullOrEmpty(command.Email))
                {
                    return ApiFail<ResetPasswordWithCodeResponse>("重置密码失败", EmptyEmailError, 400);
                }

                if (string.IsNullOrEmpty(command.Code))
                {
                    return ApiFail<ResetPasswordWithCodeResponse>("重置密码失败", EmptyCodeError, 400);
                }

                if (string.IsNullOrEmpty(command.NewPassword))
                {
                    return ApiFail<ResetPasswordWithCodeResponse>("重置密码失败", EmptyPasswordError, 400);
                }

                var result = await Mediator.Send(command);

                if (!result.IsSuccessful)
                {
                    return ApiFail<ResetPasswordWithCodeResponse>("重置密码失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "密码重置成功，请使用新密码登录");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理使用验证码重置密码请求时发生错误: {Email}", command.Email);
                return HandleException(ex, "处理使用验证码重置密码请求");
            }
        }

        /// <summary>
        /// 用户注销账户（停用账户）
        /// </summary>
        /// <param name="command">注销账户命令</param>
        /// <returns>注销结果</returns>
        [HttpPost("deactivate")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DeactivateAccountResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<DeactivateAccountResponse>>> DeactivateAccount([FromBody] DeactivateAccountCommand command)
        {
            try
            {
                // 从当前用户中获取用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiFail<DeactivateAccountResponse>("注销账户失败", InvalidUserIdError, 401);
                }

                var deactivateCommand = command with { UserId = userId };
                var result = await Mediator.Send(deactivateCommand);

                if (!result.IsSuccessful)
                {
                    return ApiFail<DeactivateAccountResponse>("注销账户失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, result.HasGracePeriod ? "账户注销已安排，您有7天时间可以取消" : "账户已立即注销");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理用户注销账户请求时发生错误");
                return HandleException(ex, "处理用户注销账户请求");
            }
        }

        /// <summary>
        /// 取消账户注销
        /// </summary>
        /// <param name="command">取消注销命令</param>
        /// <returns>取消结果</returns>
        [HttpPost("cancel-deactivation")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<CancelDeactivationResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        public async Task<ActionResult<ApiResponse<CancelDeactivationResponse>>> CancelDeactivation([FromBody] CancelDeactivationCommand command)
        {
            try
            {
                // 从当前用户中获取用户ID
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return ApiFail<CancelDeactivationResponse>("取消注销失败", InvalidUserIdError, 401);
                }

                var cancelCommand = command with { UserId = userId };
                var result = await Mediator.Send(cancelCommand);

                if (!result.IsSuccessful)
                {
                    return ApiFail<CancelDeactivationResponse>("取消注销失败", result.Errors?.ToArray() ?? ["处理请求时发生错误"], 400);
                }

                return ApiSuccess(result, "账户注销已成功取消");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "处理取消账户注销请求时发生错误");
                return HandleException(ex, "处理取消账户注销请求");
            }
        }
    }
        /// <summary>
    /// 登出请求参数类
    /// </summary>
    public class LogoutRequest
    {
        /// <summary>
        /// 用户ID - 当令牌失效时可用于登出操作
        /// </summary>
        public string? UserId { get; set; }
    }
}