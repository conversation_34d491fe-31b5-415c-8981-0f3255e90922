# 用户账户注销功能详细说明

## 问题回答

### 1. 删除用户API的行为

**当前删除用户API (`DELETE /api/v1/users/{id}`) 的行为**：

- **默认行为（软删除）**: `HardDelete = false`
  - ❌ **不删除数据库记录**
  - ✅ **将用户状态设置为 `UserStatus.Deleted`**
  - ✅ **设置 `DeletedAt` 时间戳**
  - ✅ **更新安全戳使所有令牌失效**
  - ✅ **用户无法登录，但数据保留用于审计**

- **硬删除**: `HardDelete = true`
  - ❌ **物理删除数据库记录**
  - ⚠️ **需要明确指定才会执行**

### 2. 用户自己注销账户功能

**现在已添加**用户自己注销账户的端点：

## 新增功能详细说明

### 1. 用户注销账户 (Account Deactivation)

#### API端点
- **POST /api/v1/accounts/deactivate** - 用户注销自己的账户
- **POST /api/v1/accounts/cancel-deactivation** - 取消账户注销

#### 功能特点

**安全验证**：
- 需要用户登录（JWT令牌验证）
- 需要输入当前密码确认身份
- 系统用户不能自己注销账户

**两种注销模式**：

1. **延期注销（默认）**：
   - 给用户7天反悔期
   - 账户立即无法登录
   - 7天内可以取消注销
   - 7天后自动变为永久停用

2. **立即注销**：
   - 立即停用账户
   - 无反悔期
   - 无法取消

**注销效果**：
- 用户状态变为 `Inactive`
- 更新安全戳，使所有现有令牌失效
- 用户无法登录
- 数据保留，不删除

### 2. 数据库字段

#### 新增字段
```sql
-- 在 identity_users 表中新增
deactivation_scheduled_at TIMESTAMP NULL,  -- 安排注销的时间
deactivation_reason TEXT NULL              -- 注销原因
```

### 3. API使用示例

#### 注销账户
```http
POST /api/v1/accounts/deactivate
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "password": "user_current_password",
  "reason": "不再需要此账户",
  "immediateDeactivation": false
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "账户注销已安排，您有7天时间可以取消",
  "data": {
    "isSuccessful": true,
    "userId": "user-id",
    "userName": "username",
    "status": "Active",
    "effectiveDate": "2024-01-08T10:00:00Z",
    "hasGracePeriod": true,
    "gracePeriodEnd": "2024-01-08T10:00:00Z"
  }
}
```

#### 取消注销
```http
POST /api/v1/accounts/cancel-deactivation
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "password": "user_current_password"
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "账户注销已成功取消",
  "data": {
    "isSuccessful": true,
    "userId": "user-id",
    "userName": "username",
    "status": "Active"
  }
}
```

### 4. 业务逻辑

#### 注销限制
- ✅ 普通用户（只有Customer角色）可以注销
- ❌ 系统用户（有其他角色）不能自己注销
- ❌ 已删除的用户不能注销
- ❌ 已注销的用户不能重复注销

#### 安全措施
- 密码验证确保操作安全性
- JWT令牌验证确保用户身份
- 更新安全戳使所有现有会话失效
- 记录操作日志用于审计

#### 反悔机制
- 延期注销提供7天反悔期
- 反悔期内用户可以取消注销
- 取消后账户恢复正常状态

### 5. 与删除用户API的区别

| 功能 | 用户注销账户 | 管理员删除用户 |
|------|-------------|---------------|
| **操作者** | 用户自己 | 管理员 |
| **权限要求** | 登录用户 | 管理员权限 |
| **验证方式** | 密码验证 | 管理员权限 |
| **默认行为** | 停用(Inactive) | 软删除(Deleted) |
| **反悔期** | 7天 | 无 |
| **系统用户** | 不能操作 | 可以操作 |
| **数据保留** | 完全保留 | 完全保留 |

### 6. 实现的类和文件

#### 命令和处理程序
- `DeactivateAccountCommand` / `DeactivateAccountCommandHandler`
- `CancelDeactivationCommand` / `CancelDeactivationCommandHandler`

#### 实体方法
- `ApplicationUser.ScheduleDeactivation()` - 安排延期注销
- `ApplicationUser.DeactivateImmediately()` - 立即注销
- `ApplicationUser.CancelScheduledDeactivation()` - 取消安排的注销
- `ApplicationUser.IsDeactivationDue()` - 检查是否到期
- `ApplicationUser.ExecuteScheduledDeactivation()` - 执行安排的注销

#### API控制器
- `AccountsApiController.DeactivateAccount()` - 注销账户端点
- `AccountsApiController.CancelDeactivation()` - 取消注销端点

## 总结

现在系统提供了完整的用户账户生命周期管理：

1. **管理员删除用户**: 软删除，标记为已删除状态
2. **用户自己注销**: 停用账户，可选反悔期
3. **管理员暂停用户**: 临时停用，可恢复
4. **管理员激活用户**: 恢复正常状态

所有操作都是安全的，数据都会保留，支持审计和恢复。
