using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.DeleteUser;

/// <summary>
/// 删除用户命令处理器
/// </summary>
public class DeleteUserCommandHandler : IRequestHandler<DeleteUserCommand, DeleteUserResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICacheService _cacheService;
    private readonly IPermissionCacheInvalidator _cacheInvalidator;
    private readonly ILogger<DeleteUserCommandHandler> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeleteUserCommandHandler(
        UserManager<ApplicationUser> userManager,
        ICacheService cacheService,
        IPermissionCacheInvalidator cacheInvalidator,
        ILogger<DeleteUserCommandHandler> logger)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _cacheInvalidator = cacheInvalidator ?? throw new ArgumentNullException(nameof(cacheInvalidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理删除用户命令
    /// </summary>
    public async Task<DeleteUserResponse> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("开始处理删除用户命令: {UserId}", request.UserId);

            // 获取用户
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null)
            {
                _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                return new DeleteUserResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "USER_NOT_FOUND",
                    Errors = new[] { "用户不存在" }
                };
            }

            // 检查用户是否已被删除
            if (user.Status == UserStatus.Deleted)
            {
                _logger.LogWarning("用户已被删除: {UserId}", request.UserId);
                return new DeleteUserResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "USER_ALREADY_DELETED",
                    Errors = new[] { "用户已被删除" }
                };
            }

            // 检查是否为系统管理员
            var isAdmin = await _userManager.IsInRoleAsync(user, "Administrator");
            if (isAdmin)
            {
                var adminCount = (await _userManager.GetUsersInRoleAsync("Administrator")).Count;
                if (adminCount <= 1)
                {
                    _logger.LogWarning("尝试删除最后一个管理员: {UserId}", request.UserId);
                    return new DeleteUserResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "CANNOT_DELETE_LAST_ADMIN",
                        Errors = new[] { "不能删除最后一个管理员账户" }
                    };
                }
            }

            IdentityResult result;

            if (request.HardDelete)
            {
                // 硬删除（物理删除）
                result = await _userManager.DeleteAsync(user);
                _logger.LogInformation("执行硬删除用户: {UserId}", request.UserId);
            }
            else
            {
                // 软删除（标记为已删除状态）
                user.Delete();

                // 更新安全戳，使所有现有令牌失效
                await _userManager.UpdateSecurityStampAsync(user);

                result = await _userManager.UpdateAsync(user);
                _logger.LogInformation("执行软删除用户: {UserId}, 原因: {Reason}", request.UserId, request.Reason ?? "未提供");
            }

            if (!result.Succeeded)
            {
                var errors = result.Errors.Select(e => e.Description).ToArray();
                _logger.LogError("删除用户失败: {UserId}, 错误: {Errors}", request.UserId, string.Join(", ", errors));
                return new DeleteUserResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "DELETE_FAILED",
                    Errors = errors
                };
            }

            // 清除缓存
            try
            {
                // 清除用户权限缓存
                await _cacheInvalidator.InvalidateUserPermissionsCacheAsync(Guid.Parse(request.UserId));

                // 清除用户相关缓存
                await _cacheService.RemoveByPatternAsync($"*:Users:{request.UserId}*");
                await _cacheService.RemoveByPatternAsync($"*:Users:List*");
            }
            catch (Exception ex)
            {
                // 清除缓存失败不应影响主要操作，只记录日志
                _logger.LogError(ex, "清除用户缓存时发生错误: {UserId}", request.UserId);
            }

            _logger.LogInformation("成功删除用户: {UserId}", request.UserId);
            return new DeleteUserResponse
            {
                IsSuccessful = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户时发生错误: {UserId}, 错误: {Message}", request.UserId, ex.Message);
            return new DeleteUserResponse
            {
                IsSuccessful = false,
                ErrorCode = "UNEXPECTED_ERROR",
                Errors = new[] { ex.Message }
            };
        }
    }
}
