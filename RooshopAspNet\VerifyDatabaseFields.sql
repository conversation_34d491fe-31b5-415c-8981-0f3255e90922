-- 验证 identity_users 表的新字段
-- 查看表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'identity_users' 
    AND column_name IN (
        'Status', 
        'IsSystemUser', 
        'DeletedAt', 
        'DeactivationScheduledAt', 
        'DeactivationReason'
    )
ORDER BY column_name;

-- 查看所有用户的新字段值
SELECT 
    id,
    user_name,
    email,
    "Status",
    "IsSystemUser",
    "DeletedAt",
    "DeactivationScheduledAt",
    "DeactivationReason",
    created_at
FROM identity_users
ORDER BY created_at DESC
LIMIT 10;

-- 统计用户状态分布
SELECT 
    "Status",
    COUNT(*) as user_count
FROM identity_users
GROUP BY "Status";

-- 统计系统用户数量
SELECT 
    "IsSystemUser",
    COUNT(*) as user_count
FROM identity_users
GROUP BY "IsSystemUser";
