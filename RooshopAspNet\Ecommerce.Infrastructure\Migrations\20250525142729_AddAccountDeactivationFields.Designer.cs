﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Ecommerce.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Ecommerce.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250525142729_AddAccountDeactivationFields")]
    partial class AddAccountDeactivationFields
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_trgm");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pgcrypto");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "unaccent");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "zhparser");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Carts.Cart", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("ix_carts_user_id");

                    b.ToTable("carts", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Carts.CartItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CartId")
                        .HasColumnType("uuid")
                        .HasColumnName("cart_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid")
                        .HasColumnName("merchant_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CartId")
                        .HasDatabaseName("ix_cart_items_cart_id");

                    b.HasIndex("CartId", "ProductId")
                        .HasDatabaseName("ix_cart_items_cart_id_product_id");

                    b.ToTable("cart_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.ApplicationRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsSystemRole")
                        .HasColumnType("boolean")
                        .HasColumnName("is_system_role");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_name");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("identity_roles", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer")
                        .HasColumnName("access_failed_count");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text")
                        .HasColumnName("avatar_url");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DeactivationReason")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeactivationScheduledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("email_confirmed");

                    b.Property<string>("FirstName")
                        .HasColumnType("text")
                        .HasColumnName("first_name");

                    b.Property<bool>("IsSystemUser")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_at");

                    b.Property<string>("LastName")
                        .HasColumnType("text")
                        .HasColumnName("last_name");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_user_name");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text")
                        .HasColumnName("security_stamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("user_name");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("identity_users", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.PermissionGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<bool>("IsSystemGroup")
                        .HasColumnType("boolean")
                        .HasColumnName("is_system_group");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<List<string>>("Permissions")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("permissions");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("identity_permission_groups", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.RoleInheritance", b =>
                {
                    b.Property<Guid>("ParentRoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_role_id");

                    b.Property<Guid>("ChildRoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("child_role_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.HasKey("ParentRoleId", "ChildRoleId");

                    b.HasIndex("ChildRoleId");

                    b.HasIndex("ParentRoleId");

                    b.ToTable("identity_role_inheritances", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.UserAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("is_default");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<string>("RecipientName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("recipient_name");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId");

                    b.ToTable("identity_user_addresses", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Inventory.InventoryItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AvailableQuantity")
                        .HasColumnType("integer")
                        .HasColumnName("available_quantity");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<Guid?>("ProductVariantId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_variant_id");

                    b.Property<int>("ReservedQuantity")
                        .HasColumnType("integer")
                        .HasColumnName("reserved_quantity");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_inventory_items_product_id");

                    b.HasIndex("ProductVariantId")
                        .HasDatabaseName("ix_inventory_items_product_variant_id");

                    b.HasIndex("Sku")
                        .IsUnique()
                        .HasDatabaseName("ix_inventory_items_sku");

                    b.ToTable("inventory_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Inventory.InventoryLock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<Guid>("InventoryItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("inventory_item_id");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("ix_inventory_locks_expires_at");

                    b.HasIndex("InventoryItemId")
                        .HasDatabaseName("ix_inventory_locks_inventory_item_id");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_inventory_locks_order_id");

                    b.ToTable("inventory_locks", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Invoices.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnUpdateSometimes()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("invoice_number");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceNumber")
                        .IsUnique();

                    b.HasIndex("OrderId")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Invoices.InvoiceItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<decimal>("TaxRate")
                        .HasColumnType("decimal(5,2)")
                        .HasColumnName("tax_rate");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.ToTable("invoice_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Coupon", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ApplicableCategoryIds")
                        .HasColumnType("text")
                        .HasColumnName("applicable_category_ids");

                    b.Property<string>("ApplicableProductIds")
                        .HasColumnType("text")
                        .HasColumnName("applicable_product_ids");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiry_date");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int?>("MaxUsageCount")
                        .HasColumnType("integer")
                        .HasColumnName("max_usage_count");

                    b.Property<int?>("MaxUsagePerUser")
                        .HasColumnType("integer")
                        .HasColumnName("max_usage_per_user");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<bool>("RequiresLogin")
                        .HasColumnType("boolean")
                        .HasColumnName("requires_login");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("UsageCount")
                        .HasColumnType("integer")
                        .HasColumnName("usage_count");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_shop_coupons_code");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_shop_coupons_status");

                    b.ToTable("shop_coupons", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.MarketingContent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<int>("ContentType")
                        .HasColumnType("integer")
                        .HasColumnName("content_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer")
                        .HasColumnName("display_order");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<string>("LinkUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("link_url");

                    b.Property<string>("Position")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("position");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ContentType");

                    b.HasIndex("DisplayOrder");

                    b.HasIndex("Status");

                    b.HasIndex("StartDate", "EndDate");

                    b.ToTable("marketing_contents", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Promotion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("CanBeCombined")
                        .HasColumnType("boolean")
                        .HasColumnName("can_be_combined");

                    b.Property<bool>("CanCombineWithCoupons")
                        .HasColumnType("boolean")
                        .HasColumnName("can_combine_with_coupons");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<int>("ScopeType")
                        .HasColumnType("integer")
                        .HasColumnName("scope_type");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("EndDate");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.ToTable("marketing_promotions", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.PromotionItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("ItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("item_id");

                    b.Property<int>("ItemType")
                        .HasColumnType("integer")
                        .HasColumnName("item_type");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PromotionId")
                        .HasColumnType("uuid")
                        .HasColumnName("promotion_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PromotionId");

                    b.HasIndex("ItemType", "ItemId");

                    b.ToTable("marketing_promotion_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.UserCoupon", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("CouponId")
                        .HasColumnType("uuid")
                        .HasColumnName("coupon_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UsedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("used_at");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CouponId", "ApplicationUserId", "Status")
                        .HasDatabaseName("ix_user_coupons_coupon_user_status");

                    b.ToTable("user_coupons", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Merchants.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("contact_phone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<bool>("IsPlatform")
                        .HasColumnType("boolean")
                        .HasColumnName("is_platform");

                    b.Property<DateTime?>("LastModifiedAt")
                        .IsRequired()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<bool>("RequiresProductApproval")
                        .HasColumnType("boolean")
                        .HasColumnName("requires_product_approval");

                    b.Property<string>("ResponsiblePersonName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("responsible_person_name");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsPlatform");

                    b.HasIndex("Status");

                    b.ToTable("merchants", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid")
                        .HasColumnName("merchant_id");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("notes");

                    b.Property<string>("OrderGroupNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("order_group_number");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("order_number");

                    b.Property<Guid?>("ParentOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_order_id");

                    b.Property<Guid?>("PaymentId")
                        .HasColumnType("uuid")
                        .HasColumnName("payment_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("ix_orders_user_id");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_orders_merchant_id");

                    b.HasIndex("OrderGroupNumber")
                        .HasDatabaseName("ix_orders_order_group_number");

                    b.HasIndex("OrderNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_orders_order_number");

                    b.HasIndex("ParentOrderId")
                        .HasDatabaseName("ix_orders_parent_order_id");

                    b.ToTable("orders", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.OrderHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_order_history_order_id");

                    b.ToTable("order_history", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.OrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("product_name");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_order_items_order_id");

                    b.ToTable("order_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Payments.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<DateTime?>("PaidAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("paid_at");

                    b.Property<string>("PaymentMethodCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("payment_method_code");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("transaction_id");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("PaymentMethodCode");

                    b.HasIndex("TransactionId");

                    b.ToTable("payments", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Payments.PaymentMethod", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("logo_url");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("provider_code");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<List<string>>("SupportedCountries")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("supported_countries");

                    b.Property<List<string>>("SupportedCurrencies")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("supported_currencies");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ProviderCode");

                    b.ToTable("payment_methods", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ApprovalStatus")
                        .HasColumnType("integer")
                        .HasColumnName("approval_status");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("boolean")
                        .HasColumnName("is_published");

                    b.Property<bool>("IsTaxExempt")
                        .HasColumnType("boolean")
                        .HasColumnName("is_tax_exempt");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid")
                        .HasColumnName("merchant_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<string>("ShortDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("short_description");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("slug");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("TaxCategory")
                        .HasColumnType("integer")
                        .HasColumnName("tax_category");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ApprovalStatus")
                        .HasDatabaseName("ix_products_approval_status");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_products_category_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_products_created_at");

                    b.HasIndex("IsPublished")
                        .HasDatabaseName("ix_products_is_published");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_products_merchant_id");

                    b.HasIndex("Sku")
                        .IsUnique()
                        .HasDatabaseName("ix_products_sku");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("ix_products_slug");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_products_status");

                    b.HasIndex("CategoryId", "IsPublished", "Status")
                        .HasDatabaseName("ix_products_category_published_status");

                    b.HasIndex("MerchantId", "IsPublished", "Status")
                        .HasDatabaseName("ix_products_merchant_published_status");

                    b.ToTable("shop_products", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DisplayType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("display_type");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("shop_product_attributes", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductAttributeOption", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AttributeId")
                        .HasColumnType("uuid")
                        .HasColumnName("attribute_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("value");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("AttributeId", "Value")
                        .IsUnique();

                    b.ToTable("shop_product_attribute_options", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("path");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("slug");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("Path");

                    b.HasIndex("Slug")
                        .IsUnique();

                    b.ToTable("shop_product_categories", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductImage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AltText")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("alt_text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("shop_product_images", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductVariant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Attributes")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("integer")
                        .HasColumnName("stock_quantity");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("Sku")
                        .IsUnique();

                    b.ToTable("shop_product_variants", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Returns.ReturnItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Condition")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("condition");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("notes");

                    b.Property<Guid>("OrderItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_item_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("product_name");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer")
                        .HasColumnName("quantity");

                    b.Property<Guid>("ReturnRequestId")
                        .HasColumnType("uuid")
                        .HasColumnName("return_request_id");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ReturnRequestId");

                    b.ToTable("return_items", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Returns.ReturnRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("ApprovalNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("approval_notes");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("approved_at");

                    b.Property<Guid?>("ApproverId")
                        .HasColumnType("uuid")
                        .HasColumnName("approver_id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_at");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("ItemsReceivedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("items_received_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<int>("ReasonCode")
                        .HasColumnType("integer")
                        .HasColumnName("reason_code");

                    b.Property<string>("ReasonDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("reason_description");

                    b.Property<string>("ReceiveNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("receive_notes");

                    b.Property<Guid?>("ReceiverId")
                        .HasColumnType("uuid")
                        .HasColumnName("receiver_id");

                    b.Property<string>("RefundFailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("refund_failure_reason");

                    b.Property<string>("RefundTransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("refund_transaction_id");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("rejected_at");

                    b.Property<Guid?>("RejecterId")
                        .HasColumnType("uuid")
                        .HasColumnName("rejecter_id");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("rejection_reason");

                    b.Property<string>("RequestNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("request_number");

                    b.Property<DateTime>("RequestedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("requested_at");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("integer")
                        .HasColumnName("row_version");

                    b.ComplexProperty<Dictionary<string, object>>("RefundAmount", "Ecommerce.Domain.Aggregates.Returns.ReturnRequest.RefundAmount#Money", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("refund_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("ReturnAddress", "Ecommerce.Domain.Aggregates.Returns.ReturnRequest.ReturnAddress#AddressVo", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean")
                                .HasColumnName("return_address_is_default");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("return_address_phone_number");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("return_address_recipient_name");

                            b1.ComplexProperty<Dictionary<string, object>>("Address", "Ecommerce.Domain.Aggregates.Returns.ReturnRequest.ReturnAddress#AddressVo.Address#Address", b2 =>
                                {
                                    b2.IsRequired();

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("return_address_administrative_area1");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("return_address_administrative_area2");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("return_address_administrative_area3");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("return_address_country");

                                    b2.Property<string>("CountryCode")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("return_address_country_code");

                                    b2.Property<string>("FormattedAddress")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("return_address_formatted_address");

                                    b2.Property<string>("Label")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("return_address_latitude");

                                    b2.Property<string>("Locality")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("return_address_locality");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("return_address_longitude");

                                    b2.Property<string>("Notes")
                                        .HasColumnType("text");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("return_address_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("return_address_premise");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("return_address_street");

                                    b2.Property<string>("StreetNumber")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("return_address_street_number");
                                });
                        });

                    b.ComplexProperty<Dictionary<string, object>>("Status", "Ecommerce.Domain.Aggregates.Returns.ReturnRequest.Status#ReturnStatusVo", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");
                        });

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("IX_return_requests_user_id");

                    b.HasIndex("OrderId");

                    b.HasIndex("RequestNumber")
                        .IsUnique();

                    b.ToTable("return_requests", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.Review", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<int>("Rating")
                        .HasColumnType("integer")
                        .HasColumnName("rating");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("ix_reviews_user_id");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_reviews_product_id");

                    b.ToTable("reviews", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.ReviewImage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("image_url");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ReviewId")
                        .HasColumnType("uuid")
                        .HasColumnName("review_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ReviewId")
                        .HasDatabaseName("ix_review_images_review_id");

                    b.ToTable("review_images", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.ReviewReply", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsFromMerchant")
                        .HasColumnType("boolean")
                        .HasColumnName("is_from_merchant");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ReplierId")
                        .HasColumnType("uuid")
                        .HasColumnName("replier_id");

                    b.Property<Guid>("ReviewId")
                        .HasColumnType("uuid")
                        .HasColumnName("review_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ReviewId")
                        .HasDatabaseName("ix_review_replies_review_id");

                    b.ToTable("review_replies", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Settings.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdditionalData")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("RecipientIds")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RelatedEntityId")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TargetUserGroup")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("IsGlobal");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.ToTable("Notifications", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Settings.SystemSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("Group")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("group");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("boolean")
                        .HasColumnName("is_system");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("key");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.HasIndex("Group", "Key");

                    b.ToTable("system_settings", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.Shipment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<DateTime?>("ShippingDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("shipping_date");

                    b.Property<string>("ShippingProvider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("shipping_provider");

                    b.Property<string>("TrackingNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("tracking_number");

                    b.Property<string>("TrackingUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("tracking_url");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("updated_by");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.Property<string>("_statusHistoryJson")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("status_history_json");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("ShippingDate");

                    b.HasIndex("TrackingNumber");

                    b.ToTable("shipments", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingArea", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AreaType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Attributes")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<decimal>("BaseShippingFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("ParentAreaId")
                        .HasColumnType("uuid");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("translations");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Level");

                    b.HasIndex("ParentAreaId");

                    b.ToTable("shipping_areas", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingOrigin", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("is_default");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid")
                        .HasColumnName("merchant_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId", "IsDefault");

                    b.ToTable("shipping_origins", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingProvider", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<string>("ProviderType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("provider_type");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("row_version");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("updated_by");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("ProviderType");

                    b.ToTable("shipping_providers", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApplicableRegions")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("applicable_regions");

                    b.Property<string>("Attributes")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<decimal>("BaseShippingFee")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("MaxOrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("MinOrderAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("ProductAttributeConditions")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("RuleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("ShippingProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("translations");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("MerchantId");

                    b.HasIndex("Priority");

                    b.HasIndex("RuleType");

                    b.HasIndex("ShippingProviderId");

                    b.ToTable("shipping_rules", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.ProductCategoryTaxRate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("country_code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ProductCategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_category_id");

                    b.Property<decimal>("Rate")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("rate");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("region_code");

                    b.Property<int>("TaxCategory")
                        .HasColumnType("integer")
                        .HasColumnName("tax_category");

                    b.Property<Guid>("TaxId")
                        .HasColumnType("uuid")
                        .HasColumnName("tax_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryId");

                    b.HasIndex("TaxId");

                    b.HasIndex("ProductCategoryId", "TaxCategory", "CountryCode", "RegionCode")
                        .IsUnique();

                    b.ToTable("product_category_tax_rates", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.Tax", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("taxes", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.TaxRate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("country_code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean")
                        .HasColumnName("is_default");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Rate")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)")
                        .HasColumnName("rate");

                    b.Property<string>("RegionCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("region_code");

                    b.Property<Guid>("TaxId")
                        .HasColumnType("uuid")
                        .HasColumnName("tax_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TaxId");

                    b.HasIndex("CountryCode", "RegionCode")
                        .IsUnique();

                    b.ToTable("tax_rates", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.Ticket", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid?>("BusinessReferenceId")
                        .HasColumnType("uuid")
                        .HasColumnName("business_reference_id");

                    b.Property<int?>("BusinessType")
                        .HasColumnType("integer")
                        .HasColumnName("business_type");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("subject");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("ix_tickets_user_id");

                    b.HasIndex("BusinessReferenceId")
                        .HasDatabaseName("ix_tickets_business_reference_id");

                    b.HasIndex("BusinessType")
                        .HasDatabaseName("ix_tickets_business_type");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_tickets_category_id");

                    b.ToTable("tickets", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.TicketCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessType")
                        .HasColumnType("integer")
                        .HasColumnName("business_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<bool>("RequiresBusinessReference")
                        .HasColumnType("boolean")
                        .HasColumnName("requires_business_reference");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<string>("Translations")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("translations_json");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_ticket_categories_name");

                    b.ToTable("ticket_categories", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.TicketReply", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsStaff")
                        .HasColumnType("boolean")
                        .HasColumnName("is_staff");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uuid")
                        .HasColumnName("ticket_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TicketId")
                        .HasDatabaseName("ix_ticket_replies_ticket_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_ticket_replies_user_id");

                    b.ToTable("ticket_replies", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Infrastructure.Identity.Models.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("boolean")
                        .HasColumnName("is_revoked");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_used");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("token");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("Token")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("identity_refresh_tokens", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("identity_role_claims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("identity_user_claims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text")
                        .HasColumnName("provider_key");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("provider_display_name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("identity_user_logins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("identity_user_roles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Value")
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("identity_user_tokens", (string)null);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Carts.CartItem", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Carts.Cart", null)
                        .WithMany("Items")
                        .HasForeignKey("CartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.RoleInheritance", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationRole", "ChildRole")
                        .WithMany()
                        .HasForeignKey("ChildRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationRole", "ParentRole")
                        .WithMany()
                        .HasForeignKey("ParentRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChildRole");

                    b.Navigation("ParentRole");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.UserAddress", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", null)
                        .WithMany("Addresses")
                        .HasForeignKey("ApplicationUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "Address", b1 =>
                        {
                            b1.Property<Guid>("UserAddressId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean")
                                .HasColumnName("address_is_default");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("text")
                                .HasColumnName("address_phone_number");

                            b1.Property<string>("RecipientName")
                                .HasColumnType("text")
                                .HasColumnName("address_recipient_name");

                            b1.HasKey("UserAddressId");

                            b1.ToTable("identity_user_addresses");

                            b1.WithOwner()
                                .HasForeignKey("UserAddressId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoUserAddressId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("province");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("city");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("district");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("country");

                                    b2.Property<string>("CountryCode")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("country_code");

                                    b2.Property<string>("FormattedAddress")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("detail_address");

                                    b2.Property<string>("Label")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("address_label");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("latitude");

                                    b2.Property<string>("Locality")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("locality");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("jsonb")
                                        .HasColumnName("localization_data");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("longitude");

                                    b2.Property<string>("Notes")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("address_notes");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("premise");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("street");

                                    b2.Property<string>("StreetNumber")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("street_number");

                                    b2.HasKey("AddressVoUserAddressId");

                                    b2.ToTable("identity_user_addresses");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoUserAddressId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.Navigation("Address")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Inventory.InventoryLock", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Inventory.InventoryItem", null)
                        .WithMany("Locks")
                        .HasForeignKey("InventoryItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Invoices.Invoice", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TaxAmount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("tax_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("tax_currency_code");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .ValueGeneratedOnUpdateSometimes()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.Navigation("TaxAmount")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Invoices.InvoiceItem", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Invoices.Invoice", null)
                        .WithMany("Items")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_currency_code");

                            b1.HasKey("InvoiceItemId");

                            b1.ToTable("invoice_items");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceItemId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("InvoiceItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("unit_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.HasKey("InvoiceItemId");

                            b1.ToTable("invoice_items");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceItemId");
                        });

                    b.Navigation("TotalAmount")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Coupon", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "MinOrderAmount", b1 =>
                        {
                            b1.Property<Guid>("CouponId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("min_order_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("min_order_amount_currency");

                            b1.HasKey("CouponId");

                            b1.ToTable("shop_coupons");

                            b1.WithOwner()
                                .HasForeignKey("CouponId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.DiscountRuleVo", "DiscountRule", b1 =>
                        {
                            b1.Property<Guid>("CouponId")
                                .HasColumnType("uuid");

                            b1.Property<int>("DiscountType")
                                .HasColumnType("integer")
                                .HasColumnName("discount_type");

                            b1.Property<decimal>("DiscountValue")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("discount_value");

                            b1.HasKey("CouponId");

                            b1.ToTable("shop_coupons");

                            b1.WithOwner()
                                .HasForeignKey("CouponId");
                        });

                    b.Navigation("DiscountRule")
                        .IsRequired();

                    b.Navigation("MinOrderAmount");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Promotion", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.PromotionRuleVo", "PromotionRule", b1 =>
                        {
                            b1.Property<Guid>("PromotionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("DiscountAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("discount_amount");

                            b1.Property<decimal>("DiscountPercentage")
                                .HasPrecision(5, 2)
                                .HasColumnType("numeric(5,2)")
                                .HasColumnName("discount_percentage");

                            b1.Property<decimal>("MinimumAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("minimum_amount");

                            b1.Property<int>("PromotionType")
                                .HasColumnType("integer")
                                .HasColumnName("promotion_type");

                            b1.HasKey("PromotionId");

                            b1.ToTable("marketing_promotions");

                            b1.WithOwner()
                                .HasForeignKey("PromotionId");
                        });

                    b.Navigation("PromotionRule")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.PromotionItem", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Marketing.Promotion", null)
                        .WithMany("PromotionItems")
                        .HasForeignKey("PromotionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.UserCoupon", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Marketing.Coupon", null)
                        .WithMany("UserCoupons")
                        .HasForeignKey("CouponId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.Order", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean")
                                .HasColumnName("billing_is_default");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_phone_number");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_recipient_name");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_administrative_area1");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_administrative_area2");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_administrative_area3");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_country");

                                    b2.Property<string>("CountryCode")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("billing_country_code");

                                    b2.Property<string>("FormattedAddress")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("billing_formatted_address");

                                    b2.Property<string>("Label")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_label");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("billing_latitude");

                                    b2.Property<string>("Locality")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_locality");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("jsonb")
                                        .HasColumnName("billing_localization_data");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("billing_longitude");

                                    b2.Property<string>("Notes")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("billing_notes");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("billing_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("billing_premise");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("billing_street");

                                    b2.Property<string>("StreetNumber")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("billing_street_number");

                                    b2.HasKey("AddressVoOrderId");

                                    b2.ToTable("orders");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoOrderId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean")
                                .HasColumnName("shipping_is_default");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_phone_number");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_recipient_name");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_administrative_area1");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_administrative_area2");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_administrative_area3");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_country");

                                    b2.Property<string>("CountryCode")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("shipping_country_code");

                                    b2.Property<string>("FormattedAddress")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("shipping_formatted_address");

                                    b2.Property<string>("Label")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_label");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("shipping_latitude");

                                    b2.Property<string>("Locality")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_locality");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("jsonb")
                                        .HasColumnName("shipping_localization_data");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision")
                                        .HasColumnName("shipping_longitude");

                                    b2.Property<string>("Notes")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("shipping_notes");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("shipping_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("shipping_premise");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("shipping_street");

                                    b2.Property<string>("StreetNumber")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("shipping_street_number");

                                    b2.HasKey("AddressVoOrderId");

                                    b2.ToTable("orders");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoOrderId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_amount_currency");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.OrderStatusVo", "Status", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.Navigation("BillingAddress");

                    b.Navigation("ShippingAddress");

                    b.Navigation("Status")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.OrderHistory", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Orders.Order", null)
                        .WithMany("History")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.OrderItem", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Orders.Order", null)
                        .WithMany("Items")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TotalPrice", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_price_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("OrderItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("unit_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("unit_price_currency");

                            b1.HasKey("OrderItemId");

                            b1.ToTable("order_items");

                            b1.WithOwner()
                                .HasForeignKey("OrderItemId");
                        });

                    b.Navigation("TotalPrice")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Payments.Payment", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("PaymentId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("PaymentId");

                            b1.ToTable("payments");

                            b1.WithOwner()
                                .HasForeignKey("PaymentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.PaymentSessionVo", "Session", b1 =>
                        {
                            b1.Property<Guid>("PaymentId")
                                .HasColumnType("uuid");

                            b1.Property<string>("ClientToken")
                                .HasMaxLength(2000)
                                .HasColumnType("character varying(2000)")
                                .HasColumnName("client_token");

                            b1.Property<DateTime>("CreatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("session_created_at");

                            b1.Property<DateTime?>("ExpiresAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("session_expires_at");

                            b1.Property<Dictionary<string, string>>("Metadata")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("metadata");

                            b1.Property<string>("PaymentUrl")
                                .HasMaxLength(2000)
                                .HasColumnType("character varying(2000)")
                                .HasColumnName("payment_url");

                            b1.Property<string>("SessionId")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("session_id");

                            b1.HasKey("PaymentId");

                            b1.ToTable("payments");

                            b1.WithOwner()
                                .HasForeignKey("PaymentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.PaymentStatusVo", "Status", b1 =>
                        {
                            b1.Property<Guid>("PaymentId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<string>("Status")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("status");

                            b1.HasKey("PaymentId");

                            b1.ToTable("payments");

                            b1.WithOwner()
                                .HasForeignKey("PaymentId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Session")
                        .IsRequired();

                    b.Navigation("Status")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Payments.PaymentMethod", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "MaxAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("max_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("max_amount_currency");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "MinAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("min_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("min_amount_currency");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.ProviderConfigurationVo", "ProviderConfiguration", b1 =>
                        {
                            b1.Property<Guid>("PaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<string>("_configJson")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("config_json");

                            b1.HasKey("PaymentMethodId");

                            b1.ToTable("payment_methods");

                            b1.WithOwner()
                                .HasForeignKey("PaymentMethodId");
                        });

                    b.Navigation("MaxAmount");

                    b.Navigation("MinAmount");

                    b.Navigation("ProviderConfiguration")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.Product", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "RegularPrice", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("regular_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.HasKey("ProductId");

                            b1.ToTable("shop_products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "SalePrice", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("sale_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("ProductId");

                            b1.ToTable("shop_products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Dimensions", "Dimensions", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Height")
                                .HasColumnType("numeric")
                                .HasColumnName("height");

                            b1.Property<decimal>("Length")
                                .HasColumnType("numeric")
                                .HasColumnName("length");

                            b1.Property<decimal>("Weight")
                                .HasColumnType("numeric")
                                .HasColumnName("weight");

                            b1.Property<decimal>("Width")
                                .HasColumnType("numeric")
                                .HasColumnName("width");

                            b1.HasKey("ProductId");

                            b1.ToTable("shop_products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId");
                        });

                    b.Navigation("Dimensions")
                        .IsRequired();

                    b.Navigation("RegularPrice")
                        .IsRequired();

                    b.Navigation("SalePrice");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductAttributeOption", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Products.ProductAttribute", null)
                        .WithMany("Options")
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductCategory", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Products.ProductCategory", null)
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductImage", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Products.Product", null)
                        .WithMany("Images")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductVariant", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Products.Product", null)
                        .WithMany("Variants")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("shop_product_variants");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId");
                        });

                    b.Navigation("Price")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Returns.ReturnItem", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Returns.ReturnRequest", null)
                        .WithMany("Items")
                        .HasForeignKey("ReturnRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "TotalPrice", b1 =>
                        {
                            b1.Property<Guid>("ReturnItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_price_currency_code");

                            b1.HasKey("ReturnItemId");

                            b1.ToTable("return_items");

                            b1.WithOwner()
                                .HasForeignKey("ReturnItemId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("ReturnItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("unit_price");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency_code");

                            b1.HasKey("ReturnItemId");

                            b1.ToTable("return_items");

                            b1.WithOwner()
                                .HasForeignKey("ReturnItemId");
                        });

                    b.Navigation("TotalPrice")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.Review", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.ReviewStatusVo", "ReviewStatus", b1 =>
                        {
                            b1.Property<Guid>("ReviewId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status_value");

                            b1.HasKey("ReviewId");

                            b1.ToTable("reviews");

                            b1.WithOwner()
                                .HasForeignKey("ReviewId");
                        });

                    b.Navigation("ReviewStatus")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.ReviewImage", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Reviews.Review", null)
                        .WithMany("Images")
                        .HasForeignKey("ReviewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.ReviewReply", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Reviews.Review", null)
                        .WithMany("Replies")
                        .HasForeignKey("ReviewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.Shipment", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "FromAddress", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("from_phone");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("from_name");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoShipmentId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("from_state");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("from_city");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasColumnType("text");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("from_country");

                                    b2.Property<string>("CountryCode")
                                        .HasColumnType("text");

                                    b2.Property<string>("FormattedAddress")
                                        .HasColumnType("text");

                                    b2.Property<string>("Label")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Locality")
                                        .HasColumnType("text");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Notes")
                                        .HasColumnType("text");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("from_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("from_street2");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("from_street1");

                                    b2.Property<string>("StreetNumber")
                                        .HasColumnType("text");

                                    b2.HasKey("AddressVoShipmentId");

                                    b2.ToTable("shipments");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoShipmentId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.Money", "ShippingCost", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("shipping_cost");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "ToAddress", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("to_phone");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("to_name");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoShipmentId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("to_state");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("to_city");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasColumnType("text");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("to_country");

                                    b2.Property<string>("CountryCode")
                                        .HasColumnType("text");

                                    b2.Property<string>("FormattedAddress")
                                        .HasColumnType("text");

                                    b2.Property<string>("Label")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Locality")
                                        .HasColumnType("text");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Notes")
                                        .HasColumnType("text");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("to_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("to_street2");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("to_street1");

                                    b2.Property<string>("StreetNumber")
                                        .HasColumnType("text");

                                    b2.HasKey("AddressVoShipmentId");

                                    b2.ToTable("shipments");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoShipmentId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.LabelVo", "Label", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("CreatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("label_created_at");

                            b1.Property<string>("Format")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("label_format");

                            b1.Property<string>("Id")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("label_id");

                            b1.Property<string>("Url")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("label_url");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.ParcelVo", "Parcel", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("parcel_description");

                            b1.Property<decimal>("Height")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("parcel_height");

                            b1.Property<int>("ItemCount")
                                .HasColumnType("integer")
                                .HasColumnName("parcel_item_count");

                            b1.Property<decimal>("Length")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("parcel_length");

                            b1.Property<decimal>("Weight")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("parcel_weight");

                            b1.Property<decimal>("Width")
                                .HasColumnType("decimal(10,2)")
                                .HasColumnName("parcel_width");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.ShipmentStatusVo", "Status", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<string>("Reason")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_reason");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.TrackingInfoVo", "TrackingInfo", b1 =>
                        {
                            b1.Property<Guid>("ShipmentId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Carrier")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("tracking_info_carrier");

                            b1.Property<DateTime?>("EstimatedDeliveryDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("tracking_info_estimated_delivery_date");

                            b1.Property<string>("EventsJson")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("tracking_info_events_json");

                            b1.Property<string>("Number")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("tracking_info_number");

                            b1.Property<string>("ServiceLevel")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("tracking_info_service_level");

                            b1.Property<string>("Url")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("tracking_info_url");

                            b1.HasKey("ShipmentId");

                            b1.ToTable("shipments");

                            b1.WithOwner()
                                .HasForeignKey("ShipmentId");
                        });

                    b.Navigation("FromAddress")
                        .IsRequired();

                    b.Navigation("Label");

                    b.Navigation("Parcel")
                        .IsRequired();

                    b.Navigation("ShippingCost")
                        .IsRequired();

                    b.Navigation("Status")
                        .IsRequired();

                    b.Navigation("ToAddress")
                        .IsRequired();

                    b.Navigation("TrackingInfo");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingArea", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Shipping.ShippingArea", null)
                        .WithMany()
                        .HasForeignKey("ParentAreaId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingOrigin", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.AddressVo", "Address", b1 =>
                        {
                            b1.Property<Guid>("ShippingOriginId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("IsDefault")
                                .HasColumnType("boolean");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("address_phone_number");

                            b1.Property<string>("RecipientName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_recipient_name");

                            b1.HasKey("ShippingOriginId");

                            b1.ToTable("shipping_origins");

                            b1.WithOwner()
                                .HasForeignKey("ShippingOriginId");

                            b1.OwnsOne("Ecommerce.Domain.ValueObjects.Address", "Address", b2 =>
                                {
                                    b2.Property<Guid>("AddressVoShippingOriginId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("AdministrativeArea1")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("address_state");

                                    b2.Property<string>("AdministrativeArea2")
                                        .HasMaxLength(50)
                                        .HasColumnType("character varying(50)")
                                        .HasColumnName("address_city");

                                    b2.Property<string>("AdministrativeArea3")
                                        .HasColumnType("text");

                                    b2.Property<string>("Country")
                                        .HasMaxLength(2)
                                        .HasColumnType("character varying(2)")
                                        .HasColumnName("address_country");

                                    b2.Property<string>("CountryCode")
                                        .HasColumnType("text");

                                    b2.Property<string>("FormattedAddress")
                                        .HasColumnType("text");

                                    b2.Property<string>("Label")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Latitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Locality")
                                        .HasColumnType("text");

                                    b2.Property<string>("LocalizationData")
                                        .HasColumnType("text");

                                    b2.Property<double?>("Longitude")
                                        .HasColumnType("double precision");

                                    b2.Property<string>("Notes")
                                        .HasColumnType("text");

                                    b2.Property<string>("PostalCode")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("address_postal_code");

                                    b2.Property<string>("Premise")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("address_street2");

                                    b2.Property<string>("Street")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("address_street1");

                                    b2.Property<string>("StreetNumber")
                                        .HasColumnType("text");

                                    b2.HasKey("AddressVoShippingOriginId");

                                    b2.ToTable("shipping_origins");

                                    b2.WithOwner()
                                        .HasForeignKey("AddressVoShippingOriginId");
                                });

                            b1.Navigation("Address")
                                .IsRequired();
                        });

                    b.Navigation("Address")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingProvider", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.ProviderConfigurationVo", "Config", b1 =>
                        {
                            b1.Property<Guid>("ShippingProviderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("_configJson")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("config_json");

                            b1.HasKey("ShippingProviderId");

                            b1.ToTable("shipping_providers");

                            b1.WithOwner()
                                .HasForeignKey("ShippingProviderId");
                        });

                    b.OwnsOne("Ecommerce.Domain.ValueObjects.RegionSupportVo", "RegionSupport", b1 =>
                        {
                            b1.Property<Guid>("ShippingProviderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("_regionCodesJson")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("region_codes");

                            b1.HasKey("ShippingProviderId");

                            b1.ToTable("shipping_providers");

                            b1.WithOwner()
                                .HasForeignKey("ShippingProviderId");
                        });

                    b.Navigation("Config")
                        .IsRequired();

                    b.Navigation("RegionSupport")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Shipping.ShippingRule", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Shipping.ShippingProvider", null)
                        .WithMany()
                        .HasForeignKey("ShippingProviderId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.ProductCategoryTaxRate", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Tax.Tax", null)
                        .WithMany("ProductCategoryTaxRates")
                        .HasForeignKey("TaxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.TaxRate", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Tax.Tax", null)
                        .WithMany("TaxRates")
                        .HasForeignKey("TaxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.Ticket", b =>
                {
                    b.OwnsOne("Ecommerce.Domain.ValueObjects.TicketStatusVo", "Status", b1 =>
                        {
                            b1.Property<Guid>("TicketId")
                                .HasColumnType("uuid");

                            b1.Property<DateTime>("ChangedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("status_changed_at");

                            b1.Property<string>("Description")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("status_description");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");

                            b1.HasKey("TicketId");

                            b1.ToTable("tickets");

                            b1.WithOwner()
                                .HasForeignKey("TicketId");
                        });

                    b.Navigation("Status")
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.TicketReply", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Tickets.Ticket", null)
                        .WithMany("Replies")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.HasOne("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Carts.Cart", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Identity.ApplicationUser", b =>
                {
                    b.Navigation("Addresses");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Inventory.InventoryItem", b =>
                {
                    b.Navigation("Locks");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Invoices.Invoice", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Coupon", b =>
                {
                    b.Navigation("UserCoupons");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Marketing.Promotion", b =>
                {
                    b.Navigation("PromotionItems");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Orders.Order", b =>
                {
                    b.Navigation("History");

                    b.Navigation("Items");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.Product", b =>
                {
                    b.Navigation("Images");

                    b.Navigation("Variants");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Products.ProductAttribute", b =>
                {
                    b.Navigation("Options");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Returns.ReturnRequest", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Reviews.Review", b =>
                {
                    b.Navigation("Images");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tax.Tax", b =>
                {
                    b.Navigation("ProductCategoryTaxRates");

                    b.Navigation("TaxRates");
                });

            modelBuilder.Entity("Ecommerce.Domain.Aggregates.Tickets.Ticket", b =>
                {
                    b.Navigation("Replies");
                });
#pragma warning restore 612, 618
        }
    }
}
