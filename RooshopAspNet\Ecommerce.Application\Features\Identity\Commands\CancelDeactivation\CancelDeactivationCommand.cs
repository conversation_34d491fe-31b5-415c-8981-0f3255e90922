using MediatR;

namespace Ecommerce.Application.Features.Identity.Commands.CancelDeactivation
{
    /// <summary>
    /// 取消账户注销命令
    /// </summary>
    public record CancelDeactivationCommand : IRequest<CancelDeactivationResponse>
    {
        /// <summary>
        /// 用户ID（从JWT令牌中获取）
        /// </summary>
        public string UserId { get; init; } = default!;

        /// <summary>
        /// 确认密码（安全验证）
        /// </summary>
        public string Password { get; init; } = default!;
    }
}
