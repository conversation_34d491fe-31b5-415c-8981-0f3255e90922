using MediatR;

namespace Ecommerce.Application.Features.Identity.Commands.DeactivateAccount
{
    /// <summary>
    /// 用户注销账户命令（用户自己操作）
    /// </summary>
    public record DeactivateAccountCommand : IRequest<DeactivateAccountResponse>
    {
        /// <summary>
        /// 用户ID（从JWT令牌中获取）
        /// </summary>
        public string UserId { get; init; } = default!;

        /// <summary>
        /// 注销原因
        /// </summary>
        public string? Reason { get; init; }

        /// <summary>
        /// 确认密码（安全验证）
        /// </summary>
        public string Password { get; init; } = default!;

        /// <summary>
        /// 是否立即注销（true）还是延期注销（false，给用户7天反悔期）
        /// </summary>
        public bool ImmediateDeactivation { get; init; } = false;
    }
}
