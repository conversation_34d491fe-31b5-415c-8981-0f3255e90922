# 用户管理功能增强

## 概述

本次更新为RooShopAspNet项目添加了以下功能：
1. 系统用户标识（区分系统用户和普通客户）
2. 用户状态管理（Active、Inactive、Suspended、Deleted）
3. 销户功能（软删除）
4. 停用/暂停用户功能

## 新增功能

### 1. 用户状态枚举

**文件**: `Ecommerce.Domain/Enums/UserStatus.cs`

```csharp
public enum UserStatus
{
    Active = 0,      // 活跃 - 正常使用状态
    Inactive = 1,    // 非活跃 - 暂时停用，可以重新激活
    Suspended = 2,   // 暂停 - 因违规等原因暂停使用
    Deleted = 3      // 已删除 - 销户状态，不可恢复
}
```

### 2. ApplicationUser实体增强

**新增属性**:
- `UserStatus Status` - 用户状态
- `bool IsSystemUser` - 是否为系统用户标识
- `DateTime? DeletedAt` - 删除时间

**新增方法**:
- `SetStatus(UserStatus status)` - 设置用户状态
- `SetSystemUser(bool isSystemUser)` - 设置系统用户标识
- `Activate()` - 激活用户
- `Deactivate()` - 停用用户
- `Suspend()` - 暂停用户
- `Delete()` - 删除用户（软删除）
- `IsAvailable()` - 检查用户是否可用

### 3. 系统用户判断逻辑

**文件**: `Ecommerce.Application/Extensions/UserExtensions.cs`

提供扩展方法来判断用户是否为系统用户：
- 如果用户只有Customer角色，则不是系统用户
- 如果用户有除Customer之外的其他角色，则是系统用户

### 4. 销户功能

**命令**: `DeleteUserCommand`
- 支持软删除（默认）和硬删除
- 可以提供删除原因
- 防止删除最后一个管理员

**API端点**: `DELETE /api/v1/users/{id}`

### 5. 暂停用户功能

**命令**: `SuspendUserCommand`
- 支持设置暂停原因
- 支持设置暂停到期时间
- 自动使现有令牌失效

**API端点**: `PATCH /api/v1/users/{id}/suspend`

### 6. 用户状态更新

**更新的命令**: `UpdateUserStatusCommand`
- 新增支持 "suspended" 状态
- 改进状态验证逻辑

**API端点**: `PATCH /api/v1/users/{id}/status`

## 数据库变更

### 新增字段

在 `identity_users` 表中新增：
- `status` (integer) - 用户状态枚举值
- `is_system_user` (boolean) - 系统用户标识
- `deleted_at` (timestamp) - 删除时间

### 迁移文件

已创建迁移文件：`AddUserStatusAndSystemUserFields`

## API端点

### 现有端点更新

1. **GET /api/v1/users** - 获取用户列表
   - 响应中新增 `Status`、`IsSystemUser`、`DeletedAt` 字段

2. **GET /api/v1/users/{id}** - 获取用户详情
   - 响应中新增 `Status`、`IsSystemUser`、`DeletedAt` 字段

3. **PATCH /api/v1/users/{id}/status** - 更新用户状态
   - 新增支持 "suspended" 状态

### 新增端点

1. **PATCH /api/v1/users/{id}/suspend** - 暂停用户
   ```json
   {
     "reason": "违规行为",
     "suspendUntil": "2024-12-31T23:59:59Z"
   }
   ```

2. **DELETE /api/v1/users/{id}** - 删除用户（已更新为支持软删除）
   ```json
   {
     "reason": "用户申请销户",
     "forceDelete": false,
     "hardDelete": false
   }
   ```

## 使用示例

### 1. 判断系统用户

```csharp
var isSystemUser = await userManager.IsSystemUserAsync(user);
```

### 2. 暂停用户

```csharp
var command = new SuspendUserCommand 
{ 
    UserId = "user-id",
    Reason = "违规行为",
    SuspendUntil = DateTime.UtcNow.AddDays(30)
};
var result = await mediator.Send(command);
```

### 3. 软删除用户

```csharp
var command = new DeleteUserCommand 
{ 
    UserId = "user-id",
    Reason = "用户申请销户",
    HardDelete = false
};
var result = await mediator.Send(command);
```

## 注意事项

1. **向后兼容性**: 现有用户默认状态为 `Active`，`IsSystemUser` 为 `false`
2. **权限控制**: 所有用户管理操作都需要相应的权限
3. **缓存清理**: 状态变更时会自动清理相关缓存
4. **令牌失效**: 暂停或删除用户时会更新安全戳，使现有令牌失效
5. **软删除**: 默认使用软删除，保留用户数据用于审计

## 后续建议

1. 添加用户状态变更历史记录
2. 实现定时任务清理过期的暂停状态
3. 添加批量操作功能
4. 完善前端界面以支持新功能
5. 添加用户状态变更通知功能
