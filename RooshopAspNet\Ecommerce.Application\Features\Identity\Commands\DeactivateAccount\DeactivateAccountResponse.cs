using System.Collections.Generic;

namespace Ecommerce.Application.Features.Identity.Commands.DeactivateAccount
{
    /// <summary>
    /// 用户注销账户响应
    /// </summary>
    public class DeactivateAccountResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 注销状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 注销生效时间
        /// </summary>
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 是否有反悔期
        /// </summary>
        public bool HasGracePeriod { get; set; }

        /// <summary>
        /// 反悔期截止时间
        /// </summary>
        public DateTime? GracePeriodEnd { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public IEnumerable<string>? Errors { get; set; }
    }
}
