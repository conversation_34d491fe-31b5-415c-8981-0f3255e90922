using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Identity;
using Ecommerce.Domain.Events.Identity;
using Ecommerce.Domain.Enums;

namespace Ecommerce.Domain.Aggregates.Identity
{
    /// <summary>
    /// 应用用户聚合根 - 继承自IdentityUser
    /// </summary>
    public class ApplicationUser : IdentityUser<Guid>
    {
        // 扩展属性
        public string? FirstName { get; private set; }
        public string? LastName { get; private set; }
        public DateTime CreatedAt { get; private set; }
        public DateTime? LastLoginAt { get; private set; }
        public string? AvatarUrl { get; private set; }
        public UserStatus Status { get; private set; }
        public bool IsSystemUser { get; private set; }
        public DateTime? DeletedAt { get; private set; }
        public DateTime? DeactivationScheduledAt { get; private set; }
        public string? DeactivationReason { get; private set; }

        // 用户地址集合 - 一对多关系
        private readonly List<UserAddress> _addresses = new();
        public IReadOnlyCollection<UserAddress> Addresses => _addresses.AsReadOnly();

        // 私有构造函数 - 防止外部直接创建实例
        private ApplicationUser() { }

        // 创建新用户的工厂方法
        public static ApplicationUser Create(
            string userName,
            string email,
            string? firstName = null,
            string? lastName = null)
        {
            if (string.IsNullOrWhiteSpace(userName))
                throw new ArgumentException("用户名不能为空", nameof(userName));

            if (string.IsNullOrWhiteSpace(email))
                throw new ArgumentException("电子邮件不能为空", nameof(email));

            var user = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = userName,
                Email = email,
                FirstName = firstName,
                LastName = lastName,
                CreatedAt = DateTime.UtcNow,
                EmailConfirmed = false,
                Status = UserStatus.Active,
                IsSystemUser = false
            };

            // 添加用户注册事件
            user.AddRegisteredEvent();

            return user;
        }

        // 添加地址
        public void AddAddress(UserAddress address)
        {
            if (address == null)
                throw new ArgumentNullException(nameof(address), "地址不能为空");

            // 如果设置为默认地址，则先将其他地址设置为非默认
            if (address.IsDefault)
            {
                foreach (var existingAddress in _addresses)
                {
                    existingAddress.UnsetAsDefault();
                }
            }

            _addresses.Add(address);
        }

        // 移除地址
        public void RemoveAddress(UserAddress address)
        {
            if (address == null)
                throw new ArgumentNullException(nameof(address), "地址不能为空");

            var addressToRemove = _addresses.FirstOrDefault(a => a.Id == address.Id);
            if (addressToRemove == null)
                throw new ArgumentException("地址不存在", nameof(address));

            _addresses.Remove(addressToRemove);

            // 如果删除的是默认地址，且还有其他地址，则将第一个地址设置为默认
            if (addressToRemove.IsDefault && _addresses.Any())
            {
                _addresses.First().SetAsDefault();
            }
        }

        // 更新地址
        public void UpdateAddress(UserAddress address)
        {
            if (address == null)
                throw new ArgumentNullException(nameof(address), "地址不能为空");

            var existingAddress = _addresses.FirstOrDefault(a => a.Id == address.Id);
            if (existingAddress == null)
                throw new ArgumentException("地址不存在", nameof(address));

            // 如果设置为默认地址，则先将其他地址设置为非默认
            if (address.IsDefault)
            {
                foreach (var addr in _addresses.Where(a => a.Id != address.Id))
                {
                    addr.UnsetAsDefault();
                }
            }

            existingAddress.UpdateAddress(
                address.RecipientName,
                address.PhoneNumber,
                address.Address.Address,
                address.IsDefault);
        }

        // 设置最后登录时间
        public void SetLastLoginTime()
        {
            LastLoginAt = DateTime.UtcNow;
        }

        // 更新用户信息
        public void UpdateProfile(string? firstName, string? lastName, string? avatarUrl)
        {
            FirstName = firstName;
            LastName = lastName;
            AvatarUrl = avatarUrl;
        }

        // 设置用户状态
        public void SetStatus(UserStatus status)
        {
            Status = status;

            // 如果设置为删除状态，记录删除时间
            if (status == UserStatus.Deleted)
            {
                DeletedAt = DateTime.UtcNow;
            }
        }

        // 设置系统用户标识
        public void SetSystemUser(bool isSystemUser)
        {
            IsSystemUser = isSystemUser;
        }

        // 激活用户
        public void Activate()
        {
            if (Status == UserStatus.Deleted)
                throw new InvalidOperationException("已删除的用户无法激活");

            Status = UserStatus.Active;
        }

        // 停用用户
        public void Deactivate()
        {
            if (Status == UserStatus.Deleted)
                throw new InvalidOperationException("已删除的用户无法停用");

            Status = UserStatus.Inactive;
        }

        // 暂停用户
        public void Suspend()
        {
            if (Status == UserStatus.Deleted)
                throw new InvalidOperationException("已删除的用户无法暂停");

            Status = UserStatus.Suspended;
        }

        // 删除用户（软删除）
        public void Delete()
        {
            Status = UserStatus.Deleted;
            DeletedAt = DateTime.UtcNow;
        }

        // 检查用户是否可用
        public bool IsAvailable()
        {
            return Status == UserStatus.Active;
        }

        // 安排注销账户（延期注销，给用户反悔期）
        public void ScheduleDeactivation(string? reason = null, int gracePeriodDays = 7)
        {
            if (Status == UserStatus.Deleted)
                throw new InvalidOperationException("已删除的用户无法安排注销");

            DeactivationScheduledAt = DateTime.UtcNow.AddDays(gracePeriodDays);
            DeactivationReason = reason;
        }

        // 立即注销账户
        public void DeactivateImmediately(string? reason = null)
        {
            if (Status == UserStatus.Deleted)
                throw new InvalidOperationException("已删除的用户无法注销");

            Status = UserStatus.Inactive;
            DeactivationReason = reason;
            DeactivationScheduledAt = null; // 清除安排的注销时间
        }

        // 取消安排的注销
        public void CancelScheduledDeactivation()
        {
            DeactivationScheduledAt = null;
            DeactivationReason = null;
        }

        // 检查是否有安排的注销且已到期
        public bool IsDeactivationDue()
        {
            return DeactivationScheduledAt.HasValue &&
                   DeactivationScheduledAt.Value <= DateTime.UtcNow &&
                   Status == UserStatus.Active;
        }

        // 执行安排的注销
        public void ExecuteScheduledDeactivation()
        {
            if (IsDeactivationDue())
            {
                Status = UserStatus.Inactive;
                DeactivationScheduledAt = null;
            }
        }

        // 添加用户注册事件
        private void AddRegisteredEvent()
        {
            // 这里可以添加用户注册时的事件
            // 例如：发送欢迎邮件、初始化用户设置等
        }
    }
}