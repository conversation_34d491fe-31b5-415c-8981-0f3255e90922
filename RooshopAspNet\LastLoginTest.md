# Last Login Time 修复验证

## 修复内容

### 1. Identity Users 表字段说明

#### access_failed_count（访问失败计数）
- **作用**: 记录用户连续登录失败的次数
- **工作机制**: 
  - 每次密码验证失败时递增
  - 成功登录后重置为0
  - 用于实现账户锁定策略

#### lockout_enabled（锁定启用）
- **作用**: 控制该用户是否启用账户锁定功能
- **工作机制**:
  - `true`: 启用锁定，失败次数达到阈值时会锁定账户
  - `false`: 禁用锁定，即使失败次数很多也不会锁定

#### lockout_end（锁定结束时间）
- **作用**: 记录账户锁定的结束时间
- **工作机制**:
  - `null`: 账户未被锁定
  - 有值且大于当前时间: 账户仍在锁定期内
  - 有值但小于当前时间: 锁定期已过，可以重新登录

### 2. 配置信息（来自 appsettings.json）
```json
"Lockout": {
  "DefaultLockoutTimeSpanInMinutes": 15,  // 默认锁定15分钟
  "MaxFailedAccessAttempts": 5,           // 最大失败尝试次数5次
  "AllowedForNewUsers": true              // 新用户启用锁定功能
}
```

### 3. Last Login At 字段修复

#### 问题描述
- `ApplicationUser` 实体有 `SetLastLoginTime()` 方法
- 但在登录流程中没有调用此方法
- 导致 `last_login_at` 字段始终为空

#### 修复方案
在以下登录成功的处理程序中添加了更新最后登录时间的逻辑：

1. **LoginCommandHandler.cs** - 普通用户名/密码登录
2. **SocialLoginCommandHandler.cs** - 社交登录
3. **ConfirmEmailCommandHandler.cs** - 邮箱确认后自动登录
4. **VerifyEmailWithCodeCommandHandler.cs** - 验证码确认邮箱后自动登录

#### 修复代码示例
```csharp
// 更新最后登录时间
user.SetLastLoginTime();
await _userManager.UpdateAsync(user);
```

## 测试验证

### 手动测试步骤
1. 使用API进行用户登录
2. 查询数据库中的 `identity_users` 表
3. 验证 `last_login_at` 字段是否已更新为当前登录时间

### SQL查询验证
```sql
SELECT 
    id,
    user_name,
    email,
    created_at,
    last_login_at,
    access_failed_count,
    lockout_enabled,
    lockout_end
FROM identity_users 
WHERE email = '<EMAIL>';
```

## 注意事项

1. **时区处理**: `SetLastLoginTime()` 方法使用 `DateTime.UtcNow`，确保时间统一为UTC
2. **性能考虑**: 每次登录都会执行一次数据库更新操作
3. **并发安全**: 使用 `UserManager.UpdateAsync()` 确保更新操作的安全性
4. **事务一致性**: 登录成功和更新最后登录时间在同一个事务中完成

## 相关文件修改列表

- `LoginCommandHandler.cs` - 添加最后登录时间更新
- `SocialLoginCommandHandler.cs` - 添加最后登录时间更新  
- `ConfirmEmailCommandHandler.cs` - 添加最后登录时间更新
- `VerifyEmailWithCodeCommandHandler.cs` - 添加最后登录时间更新
