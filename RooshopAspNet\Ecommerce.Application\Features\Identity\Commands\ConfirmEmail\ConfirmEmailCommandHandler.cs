using Ecommerce.Application.Interfaces.Identity;
using Ecommerce.Domain.Aggregates.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Ecommerce.Application.Features.Identity.Commands.ConfirmEmail;

/// <summary>
/// 确认邮箱命令处理程序
/// </summary>
public class ConfirmEmailCommandHandler : IRequestHandler<ConfirmEmailCommand, ConfirmEmailResponse>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenService _tokenService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="userManager">用户管理器</param>
    /// <param name="signInManager">登录管理器</param>
    /// <param name="tokenService">令牌服务</param>
    public ConfirmEmailCommandHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenService tokenService)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenService = tokenService;
    }

    /// <summary>
    /// 处理确认邮箱命令
    /// </summary>
    /// <param name="request">请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    public async Task<ConfirmEmailResponse> Handle(ConfirmEmailCommand request, CancellationToken cancellationToken)
    {
        // 验证过期时间
        if (!string.IsNullOrEmpty(request.Expires))
        {
            try
            {
                var expiryTimeString = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(request.Expires));
                var expiryTime = DateTime.Parse(expiryTimeString);

                if (expiryTime < DateTime.UtcNow)
                {
                    return new ConfirmEmailResponse
                    {
                        IsSuccessful = false,
                        Errors = ["确认链接已过期，请重新发送确认邮件"]
                    };
                }
            }
            catch (Exception)
            {
                return new ConfirmEmailResponse
                {
                    IsSuccessful = false,
                    Errors = ["无效的过期时间参数"]
                };
            }
        }

        var user = await _userManager.FindByIdAsync(request.UserId.ToString());
        if (user == null)
        {
            return new ConfirmEmailResponse
            {
                IsSuccessful = false,
                Errors = ["找不到指定的用户"]
            };
        }

        // 解码令牌
        var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(request.Token));

        // 确认邮箱
        var result = await _userManager.ConfirmEmailAsync(user, decodedToken);
        if (!result.Succeeded)
        {
            return new ConfirmEmailResponse
            {
                IsSuccessful = false,
                Errors = result.Errors.Select(e => e.Description)
            };
        }

        // 自动登录用户
        await _signInManager.SignInAsync(user, isPersistent: false);

        // 更新最后登录时间
        user.SetLastLoginTime();
        await _userManager.UpdateAsync(user);

        // 生成访问令牌
        var roles = await _userManager.GetRolesAsync(user);
        var accessToken = await _tokenService.CreateAccessTokenAsync(user.Id, user.UserName!, user.Email!, roles);
        var refreshToken = await _tokenService.CreateRefreshTokenAsync(user);
        var expiresIn = _tokenService.GetAccessTokenExpirationSeconds();

        return new ConfirmEmailResponse
        {
            IsSuccessful = true,
            UserId = user.Id.ToString(),
            UserName = user.UserName!,
            Email = user.Email!,
            Token = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = expiresIn,
            Roles = roles.ToArray()
        };
    }
}
