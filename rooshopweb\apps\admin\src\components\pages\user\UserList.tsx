'use client';

import { useState, useMemo, useCallback } from 'react';
import { usePagedQuery, useApiMutation } from '@rooshop/core/data';
import { useAdminTranslations } from '@rooshop/core/i18n';
import { PermissionGuard, withPermission } from '@rooshop/core';
import { formatErrorMessage, apiClient } from '@rooshop/core/api';
import {
  DataTable,
  DataTableToolbar,
  DataTableColumnHeader,
  type Option
} from '@app/components/tables';
import { Button } from '@app/components/ui/button';
import { Badge } from '@app/components/ui/badge';
import { Checkbox } from '@app/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@app/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@app/components/ui/dialog';
import { Input } from '@app/components/ui/input';
import { Label } from '@app/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@app/components/ui/select';
import { Plus, MoreVertical, Edit, Trash, User, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { useReactTable, getCoreRowModel, getSortedRowModel, getFilteredRowModel, getPaginationRowModel, type ColumnDef } from '@tanstack/react-table';

// 用户数据类型定义
interface User {
  id: string;
  userName: string; // 后端返回的字段名
  email: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean; // 后端返回的字段名
  roles?: string[];
  createdAt: string;
  lastLoginAt?: string;
}

// 用户状态选项
const statusOptions: Option[] = [
  { label: '活跃', value: 'Active' },
  { label: '非活跃', value: 'Inactive' },
  { label: '待审核', value: 'Pending' },
];

function UserList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState<{
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    status: 'Active' | 'Inactive' | 'Pending';
  }>({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    status: 'Active'
  });

  const t = useAdminTranslations('user');

  // 数据获取
  const { data, error, mutate } = usePagedQuery<User>('/UsersApi', {
    page: 1,
    pageSize: 10
  });

  // API操作
  const createUser = useApiMutation('/UsersApi', { method: 'POST' });

  // 事件处理函数
  const handleEdit = useCallback((user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.userName,
      email: user.email,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      status: user.isActive ? 'Active' : 'Inactive'
    });
    setEditDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      // 直接使用 apiClient 进行 DELETE 请求
      await apiClient.delete(`/UsersApi/${id}`);
      toast.success(t('deleteSuccess'));
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  }, [t, mutate]);

  // 表格列定义
  const columns = useMemo<ColumnDef<User>[]>(() => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="选择全部"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'userName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('username')} />
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.userName}</span>
        </div>
      ),
      meta: {
        label: t('username'),
        variant: 'text',
        placeholder: '搜索用户名...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('email')} />
      ),
      meta: {
        label: t('email'),
        variant: 'text',
        placeholder: '搜索邮箱...',
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'firstName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('firstName')} />
      ),
      cell: ({ row }) => row.original.firstName || '-',
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('lastName')} />
      ),
      cell: ({ row }) => row.original.lastName || '-',
    },
    {
      accessorKey: 'isActive',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('statusLabel')} />
      ),
      cell: ({ row }) => {
        const isActive = row.original.isActive;
        const status = isActive ? 'active' : 'inactive';
        const variant = isActive ? 'default' : 'secondary';
        return (
          <Badge variant={variant}>
            {t(`status.${status}`)}
          </Badge>
        );
      },
      meta: {
        label: '状态', // 直接使用中文，避免翻译路径问题
        variant: 'multiSelect',
        options: statusOptions,
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: 'roles',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('roles')} />
      ),
      cell: ({ row }) => {
        const roles = row.original.roles || [];
        return roles.length > 0 ? (
          <div className="flex gap-1 flex-wrap">
            {roles.slice(0, 2).map((role, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                {role}
              </Badge>
            ))}
            {roles.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{roles.length - 2}
              </Badge>
            )}
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
      enableSorting: false,
    },
    {
      id: 'actions',
      header: t('actions'),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <PermissionGuard requiredPermission="Permissions.Users.Edit">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="h-4 w-4 mr-2" />
                {t('edit')}
              </DropdownMenuItem>
            </PermissionGuard>
            <PermissionGuard requiredPermission="Permissions.Users.Delete">
              <DropdownMenuItem
                onClick={() => handleDelete(row.original.id)}
                className="text-destructive"
              >
                <Trash className="h-4 w-4 mr-2" />
                {t('delete')}
              </DropdownMenuItem>
            </PermissionGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
      enableHiding: false,
    }
  ], [t, handleEdit, handleDelete]);

  // 初始化表格
  const table = useReactTable({
    data: data?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: true,
    getRowId: (row) => row.id,
  });

  // 其他事件处理函数
  const handleCreate = useCallback(async () => {
    try {
      // 将前端数据格式转换为后端期望的格式
      const createData = {
        userName: formData.username,
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        isActive: formData.status === 'Active'
      };
      await createUser.trigger(createData);
      toast.success(t('createSuccess'));
      setCreateDialogOpen(false);
      setFormData({ username: '', email: '', firstName: '', lastName: '', status: 'Active' });
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    }
  }, [createUser, formData, t, mutate]);

  const handleUpdate = useCallback(async () => {
    if (!selectedUser) return;

    try {
      setIsUpdating(true);
      // 将前端数据格式转换为后端期望的格式
      const updateData = {
        userName: formData.username,
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        isActive: formData.status === 'Active'
      };
      // 使用 apiClient 进行 PUT 请求，ID 在 URL 中
      await apiClient.put(`/UsersApi/${selectedUser.id}`, updateData);
      toast.success(t('updateSuccess'));
      setEditDialogOpen(false);
      setSelectedUser(null);
      mutate();
    } catch (error) {
      toast.error(formatErrorMessage(error));
    } finally {
      setIsUpdating(false);
    }
  }, [selectedUser, formData, t, mutate, setIsUpdating]);

  if (error) {
    return (
      <div className="flex h-64 w-full items-center justify-center">
        <div className="text-center">
          <p className="text-destructive">{formatErrorMessage(error)}</p>
          <Button onClick={() => mutate()} className="mt-2">
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 数据表格 */}
      <DataTable table={table}>
        <DataTableToolbar table={table}>
          <PermissionGuard requiredPermission="Permissions.Users.Create">
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => setCreateDialogOpen(true)}
            >
              <Plus className="mr-2 size-4" />
              {t('create')}
            </Button>
          </PermissionGuard>
        </DataTableToolbar>
      </DataTable>

      {/* 创建用户对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('create')}</DialogTitle>
            <DialogDescription>
              {t('createDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('username')}</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder={t('usernamePlaceholder')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('email')}</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder={t('emailPlaceholder')}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('firstName')}</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder={t('firstNamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">{t('lastName')}</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder={t('lastNamePlaceholder')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">{t('statusLabel')}</Label>
              <Select value={formData.status} onValueChange={(value: 'Active' | 'Inactive' | 'Pending') =>
                setFormData(prev => ({ ...prev, status: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={handleCreate} disabled={createUser.isMutating}>
              {createUser.isMutating ? t('creating') : t('create')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('edit')}</DialogTitle>
            <DialogDescription>
              {t('editDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-username">{t('username')}</Label>
              <Input
                id="edit-username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder={t('usernamePlaceholder')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-email">{t('email')}</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder={t('emailPlaceholder')}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-firstName">{t('firstName')}</Label>
                <Input
                  id="edit-firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder={t('firstNamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-lastName">{t('lastName')}</Label>
                <Input
                  id="edit-lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder={t('lastNamePlaceholder')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-status">{t('statusLabel')}</Label>
              <Select value={formData.status} onValueChange={(value: 'Active' | 'Inactive' | 'Pending') =>
                setFormData(prev => ({ ...prev, status: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={handleUpdate} disabled={isUpdating}>
              {isUpdating ? t('updating') : t('update')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default withPermission(UserList, "Permissions.Users.View");