using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ecommerce.Application.Cache;
using Ecommerce.Application.Interfaces.Cache;
using Ecommerce.Domain.Aggregates.Identity;
using Ecommerce.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;

namespace Ecommerce.Application.Features.Identity.Commands.CancelDeactivation
{
    /// <summary>
    /// 取消账户注销命令处理器
    /// </summary>
    public class CancelDeactivationCommandHandler : IRequestHandler<CancelDeactivationCommand, CancelDeactivationResponse>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICacheService _cacheService;
        private readonly ILogger<CancelDeactivationCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CancelDeactivationCommandHandler(
            UserManager<ApplicationUser> userManager,
            ICacheService cacheService,
            ILogger<CancelDeactivationCommandHandler> logger)
        {
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 处理取消账户注销命令
        /// </summary>
        public async Task<CancelDeactivationResponse> Handle(CancelDeactivationCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理取消账户注销命令: {UserId}", request.UserId);

                // 获取用户
                var user = await _userManager.FindByIdAsync(request.UserId);
                if (user == null)
                {
                    _logger.LogWarning("用户不存在: {UserId}", request.UserId);
                    return new CancelDeactivationResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "USER_NOT_FOUND",
                        Errors = new[] { "用户不存在" }
                    };
                }

                // 验证密码
                var passwordValid = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordValid)
                {
                    _logger.LogWarning("取消注销时密码验证失败: {UserId}", request.UserId);
                    return new CancelDeactivationResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "INVALID_PASSWORD",
                        Errors = new[] { "密码验证失败" }
                    };
                }

                // 检查是否有安排的注销
                if (!user.DeactivationScheduledAt.HasValue)
                {
                    _logger.LogWarning("用户没有安排的注销: {UserId}", request.UserId);
                    return new CancelDeactivationResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "NO_SCHEDULED_DEACTIVATION",
                        Errors = new[] { "没有安排的账户注销" }
                    };
                }

                // 检查是否已经过期（已经注销）
                if (user.Status == UserStatus.Inactive)
                {
                    _logger.LogWarning("用户账户已被注销，无法取消: {UserId}", request.UserId);
                    return new CancelDeactivationResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "ALREADY_DEACTIVATED",
                        Errors = new[] { "账户已被注销，无法取消" }
                    };
                }

                // 取消安排的注销
                user.CancelScheduledDeactivation();

                // 更新用户
                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = result.Errors.Select(e => e.Description).ToArray();
                    _logger.LogError("取消账户注销失败: {UserId}, 错误: {Errors}", request.UserId, string.Join(", ", errors));
                    return new CancelDeactivationResponse
                    {
                        IsSuccessful = false,
                        ErrorCode = "CANCEL_FAILED",
                        Errors = errors
                    };
                }

                // 清除缓存
                try
                {
                    await _cacheService.RemoveByPatternAsync($"*:Users:{request.UserId}*");
                    await _cacheService.RemoveByPatternAsync($"*:Users:List*");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清除用户缓存时发生错误: {UserId}", request.UserId);
                }

                _logger.LogInformation("成功取消用户账户注销: {UserId}", request.UserId);

                return new CancelDeactivationResponse
                {
                    IsSuccessful = true,
                    UserId = user.Id.ToString(),
                    UserName = user.UserName,
                    Status = user.Status.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理取消账户注销时发生错误: {UserId}, 错误: {Message}", request.UserId, ex.Message);
                return new CancelDeactivationResponse
                {
                    IsSuccessful = false,
                    ErrorCode = "UNEXPECTED_ERROR",
                    Errors = new[] { ex.Message }
                };
            }
        }
    }
}
